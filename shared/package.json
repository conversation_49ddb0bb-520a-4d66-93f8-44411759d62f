{"name": "@passa/shared", "version": "1.0.0", "description": "Shared utilities and types for Passa", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "echo 'No source files to lint yet'", "lint:fix": "echo 'No source files to lint yet'", "format": "prettier --write src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.22.4", "stellar-sdk": "^11.2.2", "lodash": "^4.17.21", "date-fns": "^2.30.0"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "typescript": "^5.3.3", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0"}}