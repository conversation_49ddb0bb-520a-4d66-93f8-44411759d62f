# Passa Environment Configuration

# Application
NODE_ENV=development
PORT=3000
APP_NAME=Passa
APP_URL=http://localhost:3000
API_URL=http://localhost:3001

# Database Configuration
DATABASE_URL=postgresql://passa:password@localhost:5432/passa_dev
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=passa_dev
DATABASE_USER=passa
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ClickHouse Configuration (Analytics)
CLICKHOUSE_URL=http://localhost:8123
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_DATABASE=passa_analytics
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=

# Stellar Network Configuration
STELLAR_NETWORK=testnet
STELLAR_HORIZON_URL=https://horizon-testnet.stellar.org
SOROBAN_RPC_URL=https://soroban-testnet.stellar.org
STELLAR_PASSPHRASE=Test SDF Network ; September 2015

# Stellar Account Configuration
PLATFORM_SECRET_KEY=your_platform_secret_key_here
PLATFORM_PUBLIC_KEY=your_platform_public_key_here
ISSUER_SECRET_KEY=your_issuer_secret_key_here
ISSUER_PUBLIC_KEY=your_issuer_public_key_here

# Smart Contract Addresses
REVENUE_CONTRACT_ID=your_revenue_contract_id
ATTRIBUTION_CONTRACT_ID=your_attribution_contract_id
LOYALTY_CONTRACT_ID=your_loyalty_contract_id
TICKET_CONTRACT_ID=your_ticket_contract_id

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your_refresh_token_secret_here
REFRESH_TOKEN_EXPIRES_IN=30d

# Session Configuration
SESSION_SECRET=your_session_secret_here
SESSION_MAX_AGE=********

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Passa

# File Upload Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=passa-uploads
CLOUDFRONT_DOMAIN=your_cloudfront_domain

# Payment Configuration
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_PASSKEY=your_mpesa_passkey
MPESA_CALLBACK_URL=https://your-domain.com/api/payments/mpesa/callback

# Social Media API Keys
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token
TIKTOK_CLIENT_KEY=your_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret

# Analytics & Monitoring
GOOGLE_ANALYTICS_ID=your_google_analytics_id
SENTRY_DSN=your_sentry_dsn
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_CREATOR_VERIFICATION=true
ENABLE_BRAND_CAMPAIGNS=true
ENABLE_LOYALTY_REWARDS=true
ENABLE_ANALYTICS=true

# Development Tools
ENABLE_SWAGGER=true
ENABLE_GRAPHQL_PLAYGROUND=true
ENABLE_DEBUG_LOGS=true
