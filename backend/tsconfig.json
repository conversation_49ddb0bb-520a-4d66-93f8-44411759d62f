{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/controllers/*": ["./controllers/*"], "@/middleware/*": ["./middleware/*"], "@/models/*": ["./models/*"], "@/routes/*": ["./routes/*"], "@/services/*": ["./services/*"], "@/utils/*": ["./utils/*"], "@/config/*": ["./config/*"], "@/types/*": ["./types/*"], "@/validators/*": ["./validators/*"], "@/jobs/*": ["./jobs/*"], "@/websockets/*": ["./websockets/*"], "@/shared/*": ["../../shared/src/*"]}}, "include": ["src/**/*", "../shared/src/**/*"], "exclude": ["node_modules", "dist", "tests"], "ts-node": {"require": ["tsconfig-paths/register"]}}