# User Controller Implementation

## Overview

The User Controller is a comprehensive implementation that provides complete user management functionality for the Passa event platform. It includes profile management, dashboard data aggregation, user settings, activity tracking, search/discovery, following/follower functionality, account management, and analytics.

## Features Implemented

### ✅ User Profile CRUD Operations
- **GET /api/users/me** - Get current user data
- **PUT /api/users/me** - Update current user information
- **GET /api/users/:id** - Get public user profile
- **PUT /api/users/me/profile** - Create/update user profile
- **DELETE /api/users/me/profile** - Delete user profile (set to private)

### ✅ User Dashboard Data Aggregation
- **GET /api/users/me/dashboard** - Get comprehensive dashboard data
  - User stats (followers, following, events, tickets)
  - Recent activity
  - Upcoming events
  - Recommended events
  - Recent followers
  - Notifications count

### ✅ User Settings Management
- **GET /api/users/me/settings** - Get user settings
- **PUT /api/users/me/settings** - Update user preferences, notifications, privacy

### ✅ User Activity Tracking
- **GET /api/users/me/activity** - Get user activity history
- **GET /api/users/me/analytics** - Get detailed user analytics
- Automatic activity logging for all user actions

### ✅ User Search and Discovery
- **GET /api/users/search** - Search users with advanced filters
- **GET /api/users/me/discovery** - Get personalized user recommendations
- Support for filtering by location, verification status, events

### ✅ Following/Follower Functionality
- **POST /api/users/:id/follow** - Follow a user
- **DELETE /api/users/:id/follow** - Unfollow a user
- **GET /api/users/:id/followers** - Get user's followers
- **GET /api/users/:id/following** - Get users being followed
- **GET /api/users/:id/follow-status** - Check follow status

### ✅ Account Deactivation/Deletion
- **POST /api/users/me/deactivate** - Deactivate account
- **POST /api/users/me/reactivate** - Reactivate account
- **DELETE /api/users/me/account** - Delete account (soft delete)
- **PUT /api/users/me/password** - Change password

### ✅ User Statistics and Analytics
- **GET /api/users/:id/stats** - Get user statistics
- Activity scores, engagement metrics
- Growth analytics with date ranges
- Leaderboards and trending users

## Technical Implementation

### Database Schema

#### New Tables Added
- **user_follows** - Manages following/follower relationships
  - Prevents self-following
  - Supports soft delete (is_active flag)
  - Indexed for performance

#### Enhanced Tables
- **user_profiles** - Extended with privacy settings
- **audit_logs** - Enhanced activity tracking

### Models

#### Core Models
- **UserController** - Main controller with all endpoints
- **UserFollowModel** - Following/follower functionality
- **UserStatsModel** - Statistics and analytics
- **UserSearchModel** - Search and discovery
- **UserDashboardModel** - Dashboard data aggregation

#### Utility Models
- **UserValidationExtended** - Advanced validation
- **CacheManager** - Performance optimization
- **QueryOptimizer** - Database query optimization

### Security Features

#### Authorization Middleware
- **requireOwnership** - Ensures users can only access their own data
- **checkProfileAccess** - Validates profile visibility
- **validateUserExists** - Checks user existence and status
- **requireAdmin** - Admin-only access control

#### Rate Limiting
- Sensitive operations (follow/unfollow, password change)
- Account management operations
- Configurable limits and windows

#### Input Validation
- **validateUserUpdate** - User data validation
- **validateProfileUpdate** - Profile data validation
- **sanitizeInput** - Input sanitization

### Performance Optimizations

#### Caching Strategy
- Dashboard data caching (5 minutes)
- User stats caching (15 minutes)
- Search results caching (1 minute)
- Automatic cache invalidation

#### Database Optimizations
- Optimized queries with proper indexing
- Parallel data fetching
- Batch operations for better performance
- Query monitoring and logging

## API Endpoints

### Public Endpoints
```
GET /api/users/search - Search users
GET /api/users/:id - Get user profile
GET /api/users/:id/profile - Get user profile details
GET /api/users/:id/followers - Get user followers
GET /api/users/:id/following - Get users being followed
GET /api/users/:id/stats - Get user statistics
```

### Authenticated Endpoints
```
GET /api/users/me - Get current user
PUT /api/users/me - Update current user
GET /api/users/me/dashboard - Get dashboard data
PUT /api/users/me/profile - Update profile
DELETE /api/users/me/profile - Delete profile
GET /api/users/me/settings - Get settings
PUT /api/users/me/settings - Update settings
GET /api/users/me/activity - Get activity history
GET /api/users/me/analytics - Get analytics
GET /api/users/me/discovery - Get user recommendations
POST /api/users/:id/follow - Follow user
DELETE /api/users/:id/follow - Unfollow user
GET /api/users/:id/follow-status - Check follow status
POST /api/users/me/deactivate - Deactivate account
POST /api/users/me/reactivate - Reactivate account
DELETE /api/users/me/account - Delete account
PUT /api/users/me/password - Change password
```

### Admin Endpoints
```
GET /api/users - Get all users (admin only)
```

## Error Handling

### Custom Error Classes
- **UserNotFoundError** - User not found (404)
- **UserAccessDeniedError** - Access denied (403)
- **UserAlreadyExistsError** - Duplicate user (409)
- **FollowError** - Follow operation errors (400)
- **ProfilePrivateError** - Private profile access (403)
- **RateLimitError** - Rate limit exceeded (429)

### Validation Errors
- Input validation with detailed field-level errors
- Password strength validation
- Profile data validation
- Settings validation

## Testing

### Test Coverage
- **UserController.test.ts** - Integration tests for all endpoints
- **UserFollow.test.ts** - Unit tests for follow functionality
- **UserStats.test.ts** - Unit tests for statistics
- **Test setup utilities** - Helper functions for test data

### Test Features
- Database cleanup and setup
- Mock data generation
- Authentication token generation
- Comprehensive error scenario testing

## Usage Examples

### Following a User
```javascript
POST /api/users/123/follow
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "follow_id": 1,
    "follower_id": 456,
    "following_id": 123,
    "followed_at": "2023-12-01T10:00:00Z",
    "is_active": true
  },
  "message": "User followed successfully"
}
```

### Getting Dashboard Data
```javascript
GET /api/users/me/dashboard
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "user": { ... },
    "profile": { ... },
    "stats": {
      "followers_count": 150,
      "following_count": 75,
      "events_created": 5,
      "events_attended": 12
    },
    "recent_activity": [...],
    "upcoming_events": [...],
    "recommended_events": [...],
    "recent_followers": [...],
    "notifications_count": 3
  }
}
```

### Searching Users
```javascript
GET /api/users/search?q=john&location=New York&limit=20&sort_by=followers

Response:
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "pages": 3
  }
}
```

## Edge Cases Handled

1. **Self-following prevention** - Users cannot follow themselves
2. **Duplicate follow prevention** - Cannot follow the same user twice
3. **Private profile access** - Proper privacy controls
4. **Deleted user handling** - Graceful handling of deleted users
5. **Rate limiting** - Protection against abuse
6. **Data consistency** - Proper transaction handling
7. **Cache invalidation** - Automatic cache updates
8. **Performance monitoring** - Query performance tracking

## Future Enhancements

1. **Real-time notifications** - WebSocket integration
2. **Advanced recommendations** - ML-based user suggestions
3. **Bulk operations** - Batch follow/unfollow
4. **Export functionality** - Data export for users
5. **Advanced analytics** - More detailed insights
6. **Social features** - Groups, mentions, tags

## Deployment Considerations

1. **Database migrations** - Run migration 022 for user_follows table
2. **Environment variables** - Configure JWT secrets and cache settings
3. **Performance monitoring** - Set up query monitoring
4. **Rate limiting** - Configure appropriate limits
5. **Caching** - Consider Redis for production caching
6. **Logging** - Ensure proper log levels and monitoring
