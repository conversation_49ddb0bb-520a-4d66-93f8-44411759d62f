import { db } from '@/config/database';
import { DashboardData } from '@/types/user';
import { UserModel } from './User';
import { UserProfileModel } from './UserProfile';
import { UserStatsModel } from './UserStats';
import { UserActivityModel } from './UserActivity';
import { UserFollowModel } from './UserFollow';
import { logger } from '@/utils/logger';
import { cacheManager, CacheKeys, CacheTTL } from '@/utils/cacheManager';
import { QueryOptimizer } from '@/utils/queryOptimizer';

export class UserDashboardModel {
  private static readonly EVENTS_TABLE = 'events';
  private static readonly TICKETS_TABLE = 'tickets';

  /**
   * Get comprehensive dashboard data for a user (with caching)
   */
  static async getDashboardData(userId: number): Promise<DashboardData> {
    const cacheKey = CacheKeys.userDashboard(userId);

    return cacheManager.cached(cacheKey, async () => {
      try {
        return QueryOptimizer.monitorQuery('getDashboardData', async () => {
          // Use optimized parallel data fetching
          const optimizedData = await QueryOptimizer.getOptimizedDashboardData(userId);

          // Fetch remaining data in parallel
          const [
            user,
            profile,
            recommendedEvents,
            notificationsCount
          ] = await Promise.all([
            UserModel.findById(userId),
            UserProfileModel.findByUserId(userId),
            this.getRecommendedEvents(userId),
            this.getNotificationsCount(userId)
          ]);

          if (!user) {
            throw new Error('User not found');
          }

          return {
            user,
            profile: profile || await this.createDefaultProfile(userId),
            stats: optimizedData.userStats,
            recent_activity: optimizedData.recentActivity,
            upcoming_events: optimizedData.upcomingEvents,
            recommended_events: recommendedEvents,
            recent_followers: optimizedData.recentFollowers,
            notifications_count: notificationsCount
          };
        });
      } catch (error) {
        logger.error('Error getting dashboard data:', error);
        throw error;
      }
    }, CacheTTL.MEDIUM);
  }

  /**
   * Get upcoming events for user (events they're attending or organizing)
   */
  private static async getUpcomingEvents(userId: number): Promise<any[]> {
    const now = new Date();

    // Get events user is attending (has tickets for)
    const attendingEvents = await db(this.EVENTS_TABLE)
      .join(this.TICKETS_TABLE, `${this.EVENTS_TABLE}.event_id`, `${this.TICKETS_TABLE}.event_id`)
      .where(`${this.TICKETS_TABLE}.purchaser_user_id`, userId)
      .where(`${this.EVENTS_TABLE}.start_datetime`, '>', now)
      .where(`${this.EVENTS_TABLE}.is_deleted`, false)
      .where(`${this.TICKETS_TABLE}.is_deleted`, false)
      .select(
        `${this.EVENTS_TABLE}.*`,
        db.raw('\'attending\' as user_relation')
      )
      .groupBy(`${this.EVENTS_TABLE}.event_id`)
      .orderBy(`${this.EVENTS_TABLE}.start_datetime`, 'asc')
      .limit(5);

    // Get events user is organizing
    const organizingEvents = await db(this.EVENTS_TABLE)
      .where('organizer_user_id', userId)
      .where('start_datetime', '>', now)
      .where('is_deleted', false)
      .select(
        '*',
        db.raw('\'organizing\' as user_relation')
      )
      .orderBy('start_datetime', 'asc')
      .limit(5);

    // Combine and sort by date
    const allEvents = [...attendingEvents, ...organizingEvents];
    return allEvents
      .sort((a, b) => new Date(a.start_datetime).getTime() - new Date(b.start_datetime).getTime())
      .slice(0, 10);
  }

  /**
   * Get recommended events based on user's interests and activity
   */
  private static async getRecommendedEvents(userId: number): Promise<any[]> {
    const now = new Date();

    // Get user's location for location-based recommendations
    const userProfile = await db('user_profiles')
      .where('user_id', userId)
      .first();

    let query = db(this.EVENTS_TABLE)
      .where('start_datetime', '>', now)
      .where('is_deleted', false)
      .where('status', 'published');

    // Location-based recommendations
    if (userProfile?.location) {
      query = query.where('location', 'ILIKE', `%${userProfile.location}%`);
    }

    // Get events from categories the user has shown interest in
    const userInterests = await this.getUserEventInterests(userId);
    if (userInterests.length > 0) {
      query = query.whereIn('category_id', userInterests);
    }

    // Exclude events user is already attending or organizing
    query = query.whereNotExists(function() {
      this.select('*')
        .from(this.TICKETS_TABLE)
        .whereRaw(`${this.TICKETS_TABLE}.event_id = ${this.EVENTS_TABLE}.event_id`)
        .where(`${this.TICKETS_TABLE}.purchaser_user_id`, userId)
        .where(`${this.TICKETS_TABLE}.is_deleted`, false);
    }).whereNot('organizer_user_id', userId);

    const recommendedEvents = await query
      .select('*')
      .orderBy('start_datetime', 'asc')
      .limit(10);

    return recommendedEvents;
  }

  /**
   * Get user's event interests based on past ticket purchases
   */
  private static async getUserEventInterests(userId: number): Promise<number[]> {
    const interests = await db(this.EVENTS_TABLE)
      .join(this.TICKETS_TABLE, `${this.EVENTS_TABLE}.event_id`, `${this.TICKETS_TABLE}.event_id`)
      .where(`${this.TICKETS_TABLE}.purchaser_user_id`, userId)
      .where(`${this.TICKETS_TABLE}.is_deleted`, false)
      .whereNotNull(`${this.EVENTS_TABLE}.category_id`)
      .select(`${this.EVENTS_TABLE}.category_id`)
      .groupBy(`${this.EVENTS_TABLE}.category_id`)
      .orderByRaw('COUNT(*) DESC')
      .limit(5);

    return interests.map(item => item.category_id);
  }

  /**
   * Get notifications count (placeholder - would need notification system)
   */
  private static async getNotificationsCount(userId: number): Promise<number> {
    // This would require implementing a notifications system
    // For now, return count based on recent followers and activity
    const recentFollowersCount = await db('user_follows')
      .where('following_id', userId)
      .where('is_active', true)
      .where('followed_at', '>', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) // Last 7 days
      .count('* as count')
      .first();

    return parseInt(recentFollowersCount?.['count'] as string || '0', 10);
  }

  /**
   * Create default profile if none exists
   */
  private static async createDefaultProfile(userId: number) {
    try {
      return await UserProfileModel.create({
        user_id: userId,
        language: 'en',
        is_public: true
      });
    } catch (error) {
      logger.error('Error creating default profile:', error);
      // Return a minimal profile object if creation fails
      return {
        profile_id: 0,
        user_id: userId,
        language: 'en',
        is_public: true,
        profile_completion_percentage: 0,
        created_at: new Date(),
        updated_at: new Date()
      };
    }
  }

  /**
   * Get user's activity summary for dashboard
   */
  static async getActivitySummary(userId: number): Promise<{
    today: number;
    this_week: number;
    this_month: number;
    total: number;
  }> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [todayCount, weekCount, monthCount, totalCount] = await Promise.all([
      UserActivityModel.getActivityCount(userId, { dateFrom: today }),
      UserActivityModel.getActivityCount(userId, { dateFrom: thisWeek }),
      UserActivityModel.getActivityCount(userId, { dateFrom: thisMonth }),
      UserActivityModel.getActivityCount(userId)
    ]);

    return {
      today: todayCount,
      this_week: weekCount,
      this_month: monthCount,
      total: totalCount
    };
  }

  /**
   * Get quick stats for dashboard widgets
   */
  static async getQuickStats(userId: number): Promise<{
    events_created: number;
    events_attending: number;
    total_followers: number;
    total_following: number;
    profile_completion: number;
  }> {
    const [stats, followCounts, profile] = await Promise.all([
      UserStatsModel.getUserStats(userId),
      UserFollowModel.getFollowCounts(userId),
      UserProfileModel.findByUserId(userId)
    ]);

    const profileCompletion = profile 
      ? UserProfileModel.calculateProfileCompletion(profile)
      : 0;

    return {
      events_created: stats.events_created || 0,
      events_attending: stats.events_attended || 0,
      total_followers: followCounts.followers,
      total_following: followCounts.following,
      profile_completion: profileCompletion
    };
  }

  /**
   * Get personalized content recommendations
   */
  static async getPersonalizedContent(userId: number): Promise<{
    suggested_events: any[];
    suggested_users: any[];
    trending_topics: string[];
  }> {
    const [suggestedEvents, suggestedUsers] = await Promise.all([
      this.getRecommendedEvents(userId),
      this.getSuggestedUsers(userId)
    ]);

    // Get trending topics based on popular event categories
    const trendingTopics = await this.getTrendingTopics();

    return {
      suggested_events: suggestedEvents.slice(0, 5),
      suggested_users: suggestedUsers.slice(0, 5),
      trending_topics: trendingTopics
    };
  }

  /**
   * Get suggested users for the dashboard
   */
  private static async getSuggestedUsers(userId: number): Promise<any[]> {
    // Get users followed by people the current user follows
    const suggested = await db('users')
      .leftJoin('user_profiles', 'users.user_id', 'user_profiles.user_id')
      .whereIn('users.user_id', function() {
        this.select('f2.following_id')
          .from('user_follows as f1')
          .join('user_follows as f2', 'f1.following_id', 'f2.follower_id')
          .where('f1.follower_id', userId)
          .where('f1.is_active', true)
          .where('f2.is_active', true)
          .whereNot('f2.following_id', userId);
      })
      .whereNotIn('users.user_id', function() {
        this.select('following_id')
          .from('user_follows')
          .where('follower_id', userId)
          .where('is_active', true);
      })
      .where('users.is_deleted', false)
      .where('users.status', 'active')
      .select(
        'users.user_id',
        'users.username',
        'users.first_name',
        'users.last_name',
        'user_profiles.avatar_url'
      )
      .limit(10);

    return suggested;
  }

  /**
   * Get trending topics based on popular event categories
   */
  private static async getTrendingTopics(): Promise<string[]> {
    const trending = await db('events')
      .join('event_categories', 'events.category_id', 'event_categories.category_id')
      .where('events.start_datetime', '>', new Date())
      .where('events.is_deleted', false)
      .where('events.status', 'published')
      .select('event_categories.name')
      .count('* as count')
      .groupBy('event_categories.category_id', 'event_categories.name')
      .orderBy('count', 'desc')
      .limit(10);

    return trending.map(item => item.name);
  }
}
