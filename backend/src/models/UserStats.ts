import { db } from '@/config/database';
import { UserStats } from '@/types/user';
import { UserFollowModel } from './UserFollow';
import { logger } from '@/utils/logger';

export class UserStatsModel {
  private static readonly USERS_TABLE = 'users';
  private static readonly EVENTS_TABLE = 'events';
  private static readonly TICKETS_TABLE = 'tickets';
  private static readonly AUDIT_LOGS_TABLE = 'audit_logs';
  private static readonly ORGANIZERS_TABLE = 'organizers';
  private static readonly FANS_TABLE = 'fans';

  /**
   * Get comprehensive user statistics
   */
  static async getUserStats(userId: number): Promise<UserStats> {
    try {
      // Get follow counts
      const followCounts = await UserFollowModel.getFollowCounts(userId);

      // Get event statistics
      const eventStats = await this.getEventStats(userId);

      // Get ticket statistics
      const ticketStats = await this.getTicketStats(userId);

      // Get activity score
      const activityScore = await this.calculateActivityScore(userId);

      return {
        followers_count: followCounts.followers,
        following_count: followCounts.following,
        events_created: eventStats.events_created,
        events_attended: ticketStats.events_attended,
        tickets_purchased: ticketStats.tickets_purchased,
        total_spent: ticketStats.total_spent,
        profile_views: await this.getProfileViews(userId),
        activity_score: activityScore
      };
    } catch (error) {
      logger.error('Error getting user stats:', error);
      throw error;
    }
  }

  /**
   * Get event-related statistics for a user
   */
  private static async getEventStats(userId: number): Promise<{ events_created: number }> {
    // Check if user is an organizer
    const organizer = await db(this.ORGANIZERS_TABLE)
      .where('user_id', userId)
      .first();

    if (!organizer) {
      return { events_created: 0 };
    }

    const eventsCreated = await db(this.EVENTS_TABLE)
      .where('organizer_user_id', userId)
      .where('is_deleted', false)
      .count('* as count')
      .first();

    return {
      events_created: parseInt(eventsCreated?.['count'] as string || '0', 10)
    };
  }

  /**
   * Get ticket-related statistics for a user
   */
  private static async getTicketStats(userId: number): Promise<{
    events_attended: number;
    tickets_purchased: number;
    total_spent: number;
  }> {
    // Check if user is a fan
    const fan = await db(this.FANS_TABLE)
      .where('user_id', userId)
      .first();

    if (!fan) {
      return {
        events_attended: 0,
        tickets_purchased: 0,
        total_spent: 0
      };
    }

    // Get ticket statistics from tickets table
    const ticketStats = await db(this.TICKETS_TABLE)
      .where('purchaser_user_id', userId)
      .where('is_deleted', false)
      .select(
        db.raw('COUNT(DISTINCT event_id) as events_attended'),
        db.raw('COUNT(*) as tickets_purchased'),
        db.raw('SUM(price_paid) as total_spent')
      )
      .first();

    return {
      events_attended: parseInt(ticketStats?.events_attended || '0', 10),
      tickets_purchased: parseInt(ticketStats?.tickets_purchased || '0', 10),
      total_spent: parseFloat(ticketStats?.total_spent || '0')
    };
  }

  /**
   * Calculate activity score based on various factors
   */
  private static async calculateActivityScore(userId: number): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get activity count in last 30 days
    const activityCount = await db(this.AUDIT_LOGS_TABLE)
      .where('user_id', userId)
      .where('created_at', '>=', thirtyDaysAgo)
      .count('* as count')
      .first();

    const recentActivity = parseInt(activityCount?.['count'] as string || '0', 10);

    // Get follow counts
    const followCounts = await UserFollowModel.getFollowCounts(userId);

    // Calculate score based on:
    // - Recent activity (40%)
    // - Followers (30%)
    // - Following (20%)
    // - Profile completeness (10%)
    const activityWeight = Math.min(recentActivity * 2, 100) * 0.4;
    const followersWeight = Math.min(followCounts.followers * 5, 100) * 0.3;
    const followingWeight = Math.min(followCounts.following * 2, 100) * 0.2;
    
    // Profile completeness (simplified)
    const profileCompleteness = await this.getProfileCompleteness(userId);
    const profileWeight = profileCompleteness * 0.1;

    const totalScore = activityWeight + followersWeight + followingWeight + profileWeight;
    return Math.round(Math.min(totalScore, 100));
  }

  /**
   * Get profile completeness percentage
   */
  private static async getProfileCompleteness(userId: number): Promise<number> {
    const profile = await db('user_profiles')
      .where('user_id', userId)
      .first();

    if (!profile) return 0;

    const fields = ['bio', 'avatar_url', 'location', 'timezone'];
    let completedFields = 0;

    fields.forEach(field => {
      if (profile[field]) completedFields++;
    });

    return Math.round((completedFields / fields.length) * 100);
  }

  /**
   * Get profile views count (placeholder - would need view tracking)
   */
  private static async getProfileViews(userId: number): Promise<number> {
    // This would require implementing profile view tracking
    // For now, return a calculated estimate based on activity
    const activityCount = await db(this.AUDIT_LOGS_TABLE)
      .where('user_id', userId)
      .where('action', 'profile_view')
      .count('* as count')
      .first();

    return parseInt(activityCount?.['count'] as string || '0', 10);
  }

  /**
   * Get user analytics for a date range
   */
  static async getUserAnalytics(
    userId: number,
    dateFrom?: Date,
    dateTo?: Date
  ): Promise<{
    activity_by_day: Array<{ date: string; count: number }>;
    activity_by_type: Record<string, number>;
    growth_metrics: {
      new_followers: number;
      new_following: number;
      events_created: number;
      tickets_purchased: number;
    };
  }> {
    const from = dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const to = dateTo || new Date();

    // Activity by day
    const activityByDay = await db(this.AUDIT_LOGS_TABLE)
      .where('user_id', userId)
      .whereBetween('created_at', [from, to])
      .select(
        db.raw('DATE(created_at) as date'),
        db.raw('COUNT(*) as count')
      )
      .groupBy(db.raw('DATE(created_at)'))
      .orderBy('date');

    // Activity by type
    const activityByType = await db(this.AUDIT_LOGS_TABLE)
      .where('user_id', userId)
      .whereBetween('created_at', [from, to])
      .select('action')
      .count('* as count')
      .groupBy('action');

    const activityTypeMap: Record<string, number> = {};
    activityByType.forEach(item => {
      activityTypeMap[item.action] = parseInt(item.count as string, 10);
    });

    // Growth metrics
    const [newFollowers, newFollowing, eventsCreated, ticketsPurchased] = await Promise.all([
      db('user_follows')
        .where('following_id', userId)
        .whereBetween('followed_at', [from, to])
        .where('is_active', true)
        .count('* as count')
        .first(),
      db('user_follows')
        .where('follower_id', userId)
        .whereBetween('followed_at', [from, to])
        .where('is_active', true)
        .count('* as count')
        .first(),
      db(this.EVENTS_TABLE)
        .where('organizer_user_id', userId)
        .whereBetween('created_at', [from, to])
        .where('is_deleted', false)
        .count('* as count')
        .first(),
      db(this.TICKETS_TABLE)
        .where('purchaser_user_id', userId)
        .whereBetween('created_at', [from, to])
        .where('is_deleted', false)
        .count('* as count')
        .first()
    ]);

    return {
      activity_by_day: activityByDay.map(item => ({
        date: item.date,
        count: parseInt(item.count as string, 10)
      })),
      activity_by_type: activityTypeMap,
      growth_metrics: {
        new_followers: parseInt(newFollowers?.['count'] as string || '0', 10),
        new_following: parseInt(newFollowing?.['count'] as string || '0', 10),
        events_created: parseInt(eventsCreated?.['count'] as string || '0', 10),
        tickets_purchased: parseInt(ticketsPurchased?.['count'] as string || '0', 10)
      }
    };
  }

  /**
   * Get leaderboard of most active users
   */
  static async getActiveUsersLeaderboard(limit: number = 10): Promise<Array<{
    user_id: number;
    username: string;
    activity_score: number;
    followers_count: number;
  }>> {
    // This is a simplified version - in production you'd want to cache these scores
    const users = await db(this.USERS_TABLE)
      .where('is_deleted', false)
      .where('status', 'active')
      .select('user_id', 'username')
      .limit(limit * 3); // Get more users to calculate scores

    const usersWithScores = await Promise.all(
      users.map(async (user) => {
        const stats = await this.getUserStats(user.user_id);
        return {
          user_id: user.user_id,
          username: user.username,
          activity_score: stats.activity_score || 0,
          followers_count: stats.followers_count
        };
      })
    );

    return usersWithScores
      .sort((a, b) => b.activity_score - a.activity_score)
      .slice(0, limit);
  }
}
