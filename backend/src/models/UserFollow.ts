import { db } from '@/config/database';
import { UserFollow, FollowUser, PaginatedResult } from '@/types/user';
import { logger } from '@/utils/logger';

export class UserFollowModel {
  private static readonly TABLE = 'user_follows';
  private static readonly USERS_TABLE = 'users';
  private static readonly PROFILES_TABLE = 'user_profiles';

  /**
   * Follow a user
   */
  static async followUser(followerId: number, followingId: number): Promise<UserFollow> {
    try {
      // Prevent self-following
      if (followerId === followingId) {
        throw new Error('Users cannot follow themselves');
      }

      // Check if both users exist
      const [follower, following] = await Promise.all([
        db(this.USERS_TABLE).where('user_id', followerId).where('is_deleted', false).first(),
        db(this.USERS_TABLE).where('user_id', followingId).where('is_deleted', false).first()
      ]);

      if (!follower) {
        throw new Error('Follower user not found');
      }
      if (!following) {
        throw new Error('User to follow not found');
      }

      // Check if already following
      const existingFollow = await db(this.TABLE)
        .where('follower_id', followerId)
        .where('following_id', followingId)
        .first();

      if (existingFollow) {
        if (existingFollow.is_active) {
          throw new Error('Already following this user');
        } else {
          // Reactivate the follow
          const [updatedFollow] = await db(this.TABLE)
            .where('follow_id', existingFollow.follow_id)
            .update({
              is_active: true,
              followed_at: db.fn.now(),
              updated_at: db.fn.now()
            })
            .returning('*');
          return updatedFollow;
        }
      }

      // Create new follow relationship
      const [newFollow] = await db(this.TABLE)
        .insert({
          follower_id: followerId,
          following_id: followingId,
          followed_at: db.fn.now(),
          is_active: true
        })
        .returning('*');

      return newFollow;
    } catch (error) {
      logger.error('Error following user:', error);
      throw error;
    }
  }

  /**
   * Unfollow a user
   */
  static async unfollowUser(followerId: number, followingId: number): Promise<boolean> {
    try {
      const result = await db(this.TABLE)
        .where('follower_id', followerId)
        .where('following_id', followingId)
        .where('is_active', true)
        .update({
          is_active: false,
          updated_at: db.fn.now()
        });

      return result > 0;
    } catch (error) {
      logger.error('Error unfollowing user:', error);
      throw error;
    }
  }

  /**
   * Check if user is following another user
   */
  static async isFollowing(followerId: number, followingId: number): Promise<boolean> {
    const follow = await db(this.TABLE)
      .where('follower_id', followerId)
      .where('following_id', followingId)
      .where('is_active', true)
      .first();

    return !!follow;
  }

  /**
   * Get user's followers
   */
  static async getFollowers(
    userId: number,
    options: { page?: number; limit?: number } = {}
  ): Promise<PaginatedResult<FollowUser>> {
    const page = Math.max(1, options.page || 1);
    const limit = Math.min(100, Math.max(1, options.limit || 20));
    const offset = (page - 1) * limit;

    const query = db(this.TABLE)
      .join(this.USERS_TABLE, `${this.TABLE}.follower_id`, `${this.USERS_TABLE}.user_id`)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .where(`${this.TABLE}.following_id`, userId)
      .where(`${this.TABLE}.is_active`, true)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.TABLE}.followed_at`
      );

    const [data, countResult] = await Promise.all([
      query.clone().orderBy(`${this.TABLE}.followed_at`, 'desc').limit(limit).offset(offset),
      query.clone().count('* as count').first()
    ]);

    const total = parseInt(countResult?.['count'] as string || '0', 10);

    return {
      data: data.map(row => ({
        user_id: row.user_id,
        username: row.username,
        first_name: row.first_name,
        last_name: row.last_name,
        avatar_url: row.avatar_url,
        followed_at: row.followed_at
      })),
      total,
      page,
      limit
    };
  }

  /**
   * Get users that a user is following
   */
  static async getFollowing(
    userId: number,
    options: { page?: number; limit?: number } = {}
  ): Promise<PaginatedResult<FollowUser>> {
    const page = Math.max(1, options.page || 1);
    const limit = Math.min(100, Math.max(1, options.limit || 20));
    const offset = (page - 1) * limit;

    const query = db(this.TABLE)
      .join(this.USERS_TABLE, `${this.TABLE}.following_id`, `${this.USERS_TABLE}.user_id`)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .where(`${this.TABLE}.follower_id`, userId)
      .where(`${this.TABLE}.is_active`, true)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.TABLE}.followed_at`
      );

    const [data, countResult] = await Promise.all([
      query.clone().orderBy(`${this.TABLE}.followed_at`, 'desc').limit(limit).offset(offset),
      query.clone().count('* as count').first()
    ]);

    const total = parseInt(countResult?.['count'] as string || '0', 10);

    return {
      data: data.map(row => ({
        user_id: row.user_id,
        username: row.username,
        first_name: row.first_name,
        last_name: row.last_name,
        avatar_url: row.avatar_url,
        followed_at: row.followed_at
      })),
      total,
      page,
      limit
    };
  }

  /**
   * Get follow counts for a user
   */
  static async getFollowCounts(userId: number): Promise<{ followers: number; following: number }> {
    const [followersResult, followingResult] = await Promise.all([
      db(this.TABLE)
        .where('following_id', userId)
        .where('is_active', true)
        .count('* as count')
        .first(),
      db(this.TABLE)
        .where('follower_id', userId)
        .where('is_active', true)
        .count('* as count')
        .first()
    ]);

    return {
      followers: parseInt(followersResult?.['count'] as string || '0', 10),
      following: parseInt(followingResult?.['count'] as string || '0', 10)
    };
  }

  /**
   * Get mutual follows between two users
   */
  static async getMutualFollows(userId1: number, userId2: number): Promise<FollowUser[]> {
    const mutualFollows = await db(this.TABLE + ' as f1')
      .join(this.TABLE + ' as f2', function() {
        this.on('f1.following_id', '=', 'f2.following_id')
          .andOn('f1.follower_id', '!=', 'f2.follower_id');
      })
      .join(this.USERS_TABLE, 'f1.following_id', `${this.USERS_TABLE}.user_id`)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .where('f1.follower_id', userId1)
      .where('f2.follower_id', userId2)
      .where('f1.is_active', true)
      .where('f2.is_active', true)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.PROFILES_TABLE}.avatar_url`
      )
      .limit(50);

    return mutualFollows.map(row => ({
      user_id: row.user_id,
      username: row.username,
      first_name: row.first_name,
      last_name: row.last_name,
      avatar_url: row.avatar_url
    }));
  }

  /**
   * Get recent followers for a user
   */
  static async getRecentFollowers(userId: number, limit: number = 10): Promise<FollowUser[]> {
    const recentFollowers = await db(this.TABLE)
      .join(this.USERS_TABLE, `${this.TABLE}.follower_id`, `${this.USERS_TABLE}.user_id`)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .where(`${this.TABLE}.following_id`, userId)
      .where(`${this.TABLE}.is_active`, true)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.TABLE}.followed_at`
      )
      .orderBy(`${this.TABLE}.followed_at`, 'desc')
      .limit(limit);

    return recentFollowers.map(row => ({
      user_id: row.user_id,
      username: row.username,
      first_name: row.first_name,
      last_name: row.last_name,
      avatar_url: row.avatar_url,
      followed_at: row.followed_at
    }));
  }
}
