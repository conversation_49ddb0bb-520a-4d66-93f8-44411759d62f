import { db } from '@/config/database';
import { User, UserSearchFilters, PaginatedResult, UserDiscovery } from '@/types/user';
import { logger } from '@/utils/logger';

export class UserSearchModel {
  private static readonly USERS_TABLE = 'users';
  private static readonly PROFILES_TABLE = 'user_profiles';
  private static readonly FOLLOWS_TABLE = 'user_follows';
  private static readonly ORGANIZERS_TABLE = 'organizers';
  private static readonly FANS_TABLE = 'fans';

  /**
   * Search users with advanced filters
   */
  static async searchUsers(filters: UserSearchFilters = {}): Promise<PaginatedResult<User>> {
    const page = Math.max(1, filters.page || 1);
    const limit = Math.min(100, Math.max(1, filters.limit || 20));
    const offset = (page - 1) * limit;

    let query = db(this.USERS_TABLE)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .where(`${this.USERS_TABLE}.status`, 'active');

    // Text search
    if (filters.query) {
      const searchTerm = `%${filters.query}%`;
      query = query.where(function() {
        this.where(`${this.USERS_TABLE}.username`, 'ILIKE', searchTerm)
          .orWhere(`${this.USERS_TABLE}.first_name`, 'ILIKE', searchTerm)
          .orWhere(`${this.USERS_TABLE}.last_name`, 'ILIKE', searchTerm)
          .orWhere(`${this.PROFILES_TABLE}.bio`, 'ILIKE', searchTerm);
      });
    }

    // Location filter
    if (filters.location) {
      query = query.where(`${this.PROFILES_TABLE}.location`, 'ILIKE', `%${filters.location}%`);
    }

    // Verified users only
    if (filters.verified_only) {
      query = query.whereExists(function() {
        this.select('*')
          .from(this.ORGANIZERS_TABLE)
          .whereRaw(`${this.ORGANIZERS_TABLE}.user_id = ${this.USERS_TABLE}.user_id`)
          .where(`${this.ORGANIZERS_TABLE}.is_verified`, true);
      });
    }

    // Users with events
    if (filters.has_events) {
      query = query.whereExists(function() {
        this.select('*')
          .from('events')
          .whereRaw(`events.organizer_user_id = ${this.USERS_TABLE}.user_id`)
          .where('events.is_deleted', false);
      });
    }

    // Select fields
    query = query.select(
      `${this.USERS_TABLE}.user_id`,
      `${this.USERS_TABLE}.username`,
      `${this.USERS_TABLE}.email`,
      `${this.USERS_TABLE}.first_name`,
      `${this.USERS_TABLE}.last_name`,
      `${this.USERS_TABLE}.status`,
      `${this.USERS_TABLE}.email_verified`,
      `${this.USERS_TABLE}.created_at`,
      `${this.USERS_TABLE}.updated_at`,
      `${this.PROFILES_TABLE}.avatar_url`,
      `${this.PROFILES_TABLE}.bio`,
      `${this.PROFILES_TABLE}.location`
    );

    // Sorting
    const sortBy = filters.sort_by || 'created_at';
    const sortOrder = filters.sort_order || 'desc';

    switch (sortBy) {
      case 'followers':
        // Sort by follower count (requires subquery)
        query = query.orderByRaw(`(
          SELECT COUNT(*) 
          FROM ${this.FOLLOWS_TABLE} 
          WHERE following_id = ${this.USERS_TABLE}.user_id 
          AND is_active = true
        ) ${sortOrder.toUpperCase()}`);
        break;
      case 'activity':
        // Sort by recent activity
        query = query.orderByRaw(`(
          SELECT COUNT(*) 
          FROM audit_logs 
          WHERE user_id = ${this.USERS_TABLE}.user_id 
          AND created_at > NOW() - INTERVAL '30 days'
        ) ${sortOrder.toUpperCase()}`);
        break;
      case 'relevance':
        // For relevance, we'll use a combination of factors
        if (filters.query) {
          query = query.orderByRaw(`
            CASE 
              WHEN ${this.USERS_TABLE}.username ILIKE ? THEN 1
              WHEN ${this.USERS_TABLE}.first_name ILIKE ? OR ${this.USERS_TABLE}.last_name ILIKE ? THEN 2
              ELSE 3
            END ASC
          `, [`%${filters.query}%`, `%${filters.query}%`, `%${filters.query}%`]);
        } else {
          query = query.orderBy(`${this.USERS_TABLE}.created_at`, 'desc');
        }
        break;
      default:
        query = query.orderBy(`${this.USERS_TABLE}.${sortBy}`, sortOrder);
    }

    // Execute query with pagination
    const [data, countResult] = await Promise.all([
      query.clone().limit(limit).offset(offset),
      query.clone().clearSelect().clearOrder().count('* as count').first()
    ]);

    const total = parseInt(countResult?.['count'] as string || '0', 10);

    return {
      data: data.map(this.formatUser),
      total,
      page,
      limit
    };
  }

  /**
   * Get user discovery recommendations
   */
  static async getUserDiscovery(userId: number): Promise<UserDiscovery> {
    try {
      const [suggestedUsers, trendingUsers, nearbyUsers, similarInterests] = await Promise.all([
        this.getSuggestedUsers(userId),
        this.getTrendingUsers(),
        this.getNearbyUsers(userId),
        this.getUsersWithSimilarInterests(userId)
      ]);

      return {
        suggested_users: suggestedUsers,
        trending_users: trendingUsers,
        nearby_users: nearbyUsers,
        similar_interests: similarInterests
      };
    } catch (error) {
      logger.error('Error getting user discovery:', error);
      throw error;
    }
  }

  /**
   * Get suggested users based on mutual connections and interests
   */
  private static async getSuggestedUsers(userId: number, limit: number = 10): Promise<User[]> {
    // Get users followed by people the current user follows
    const suggested = await db(this.USERS_TABLE)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .whereIn(`${this.USERS_TABLE}.user_id`, function() {
        this.select('f2.following_id')
          .from(`${this.FOLLOWS_TABLE} as f1`)
          .join(`${this.FOLLOWS_TABLE} as f2`, 'f1.following_id', 'f2.follower_id')
          .where('f1.follower_id', userId)
          .where('f1.is_active', true)
          .where('f2.is_active', true)
          .whereNot('f2.following_id', userId);
      })
      .whereNotIn(`${this.USERS_TABLE}.user_id`, function() {
        this.select('following_id')
          .from(this.FOLLOWS_TABLE)
          .where('follower_id', userId)
          .where('is_active', true);
      })
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .where(`${this.USERS_TABLE}.status`, 'active')
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.USERS_TABLE}.email`,
        `${this.USERS_TABLE}.status`,
        `${this.USERS_TABLE}.email_verified`,
        `${this.USERS_TABLE}.created_at`,
        `${this.USERS_TABLE}.updated_at`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.PROFILES_TABLE}.bio`
      )
      .limit(limit);

    return suggested.map(this.formatUser);
  }

  /**
   * Get trending users (most followed recently)
   */
  private static async getTrendingUsers(limit: number = 10): Promise<User[]> {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const trending = await db(this.USERS_TABLE)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.USERS_TABLE}.email`,
        `${this.USERS_TABLE}.status`,
        `${this.USERS_TABLE}.email_verified`,
        `${this.USERS_TABLE}.created_at`,
        `${this.USERS_TABLE}.updated_at`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.PROFILES_TABLE}.bio`,
        db.raw(`(
          SELECT COUNT(*) 
          FROM ${this.FOLLOWS_TABLE} 
          WHERE following_id = ${this.USERS_TABLE}.user_id 
          AND is_active = true 
          AND followed_at > ?
        ) as recent_followers`, [sevenDaysAgo])
      )
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .where(`${this.USERS_TABLE}.status`, 'active')
      .havingRaw('recent_followers > 0')
      .orderBy('recent_followers', 'desc')
      .limit(limit);

    return trending.map(this.formatUser);
  }

  /**
   * Get nearby users based on location
   */
  private static async getNearbyUsers(userId: number, limit: number = 10): Promise<User[]> {
    // Get current user's location
    const currentUserProfile = await db(this.PROFILES_TABLE)
      .where('user_id', userId)
      .first();

    if (!currentUserProfile?.location) {
      return [];
    }

    const nearby = await db(this.USERS_TABLE)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .where(`${this.PROFILES_TABLE}.location`, 'ILIKE', `%${currentUserProfile.location}%`)
      .where(`${this.USERS_TABLE}.user_id`, '!=', userId)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .where(`${this.USERS_TABLE}.status`, 'active')
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.USERS_TABLE}.email`,
        `${this.USERS_TABLE}.status`,
        `${this.USERS_TABLE}.email_verified`,
        `${this.USERS_TABLE}.created_at`,
        `${this.USERS_TABLE}.updated_at`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.PROFILES_TABLE}.bio`,
        `${this.PROFILES_TABLE}.location`
      )
      .limit(limit);

    return nearby.map(this.formatUser);
  }

  /**
   * Get users with similar interests (simplified version)
   */
  private static async getUsersWithSimilarInterests(userId: number, limit: number = 10): Promise<User[]> {
    // This is a simplified version - in a real app you'd have more sophisticated interest matching
    const similar = await db(this.USERS_TABLE)
      .leftJoin(this.PROFILES_TABLE, `${this.USERS_TABLE}.user_id`, `${this.PROFILES_TABLE}.user_id`)
      .leftJoin(this.FANS_TABLE, `${this.USERS_TABLE}.user_id`, `${this.FANS_TABLE}.user_id`)
      .where(`${this.USERS_TABLE}.user_id`, '!=', userId)
      .where(`${this.USERS_TABLE}.is_deleted`, false)
      .where(`${this.USERS_TABLE}.status`, 'active`)
      .whereNotNull(`${this.FANS_TABLE}.interests`)
      .select(
        `${this.USERS_TABLE}.user_id`,
        `${this.USERS_TABLE}.username`,
        `${this.USERS_TABLE}.first_name`,
        `${this.USERS_TABLE}.last_name`,
        `${this.USERS_TABLE}.email`,
        `${this.USERS_TABLE}.status`,
        `${this.USERS_TABLE}.email_verified`,
        `${this.USERS_TABLE}.created_at`,
        `${this.USERS_TABLE}.updated_at`,
        `${this.PROFILES_TABLE}.avatar_url`,
        `${this.PROFILES_TABLE}.bio`
      )
      .limit(limit);

    return similar.map(this.formatUser);
  }

  /**
   * Format user data
   */
  private static formatUser(row: any): User {
    return {
      user_id: row.user_id,
      username: row.username,
      email: row.email,
      password_hash: '', // Never return password hash
      first_name: row.first_name,
      last_name: row.last_name,
      phone: row.phone,
      date_of_birth: row.date_of_birth,
      status: row.status,
      email_verified: row.email_verified,
      email_verified_at: row.email_verified_at,
      last_login_at: row.last_login_at,
      last_login_ip: row.last_login_ip,
      is_deleted: false,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }
}
