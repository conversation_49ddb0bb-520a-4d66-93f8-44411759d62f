import { Router } from 'express';
import { UserController } from '@/controllers/UserController';
import { authMiddleware, requireRole } from '@/middleware/auth';
import {
  requireOwnership,
  checkProfileAccess,
  validateUserExists,
  requireAdmin,
  rateLimitSensitive,
  validateUserUpdate,
  validateProfileUpdate,
  sanitizeInput
} from '@/middleware/userAuth';

const router = Router();

// ===== PUBLIC ROUTES =====

// Search users
router.get('/search', UserController.searchUsers);

// Get user by ID (public profile)
router.get('/:id', validateUserExists, checkProfileAccess, UserController.getUserById);

// Get user profile
router.get('/:id/profile', validateUserExists, checkProfileAccess, UserController.getProfile);

// Get user followers
router.get('/:id/followers', validateUserExists, UserController.getFollowers);

// Get user following
router.get('/:id/following', validateUserExists, UserController.getFollowing);

// Get user stats (public)
router.get('/:id/stats', validateUserExists, UserController.getUserStats);

// ===== AUTHENTICATED ROUTES =====

// Current user routes
router.get('/me', authMiddleware, UserController.getCurrentUser);
router.put('/me', authMiddleware, sanitizeInput, validateUserUpdate, UserController.updateCurrentUser);

// Dashboard
router.get('/me/dashboard', authMiddleware, UserController.getDashboard);

// Profile management
router.put('/me/profile', authMiddleware, sanitizeInput, validateProfileUpdate, UserController.updateProfile);
router.delete('/me/profile', authMiddleware, UserController.deleteProfile);

// Settings
router.get('/me/settings', authMiddleware, UserController.getSettings);
router.put('/me/settings', authMiddleware, sanitizeInput, UserController.updateSettings);

// Activity and analytics
router.get('/me/activity', authMiddleware, UserController.getActivityHistory);
router.get('/me/analytics', authMiddleware, UserController.getUserAnalytics);

// Discovery
router.get('/me/discovery', authMiddleware, UserController.getUserDiscovery);

// Following functionality
router.post('/:id/follow', authMiddleware, validateUserExists, rateLimitSensitive(10, 60000), UserController.followUser);
router.delete('/:id/follow', authMiddleware, validateUserExists, UserController.unfollowUser);
router.get('/:id/follow-status', authMiddleware, validateUserExists, UserController.checkFollowStatus);

// Account management
router.post('/me/deactivate', authMiddleware, rateLimitSensitive(3, 3600000), UserController.deactivateAccount);
router.post('/me/reactivate', authMiddleware, UserController.reactivateAccount);
router.delete('/me/account', authMiddleware, rateLimitSensitive(1, ********), UserController.deleteAccount);
router.put('/me/password', authMiddleware, rateLimitSensitive(5, 3600000), UserController.changePassword);

// ===== ADMIN ROUTES =====

// Get all users (admin only)
router.get('/', authMiddleware, requireAdmin, UserController.getAllUsers);

export default router;
