import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
import { UserModel, UserProfileModel } from '@/models';
import { logger } from '@/utils/logger';

/**
 * Middleware to ensure user can only access their own data
 */
export const requireOwnership = (paramName: string = 'id') => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      const targetUserId = parseInt(req.params[paramName], 10);

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      if (isNaN(targetUserId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      if (userId !== targetUserId) {
        return res.status(403).json({ error: 'Access denied: You can only access your own data' });
      }

      next();
    } catch (error) {
      logger.error('Error in ownership middleware:', error);
      next(error);
    }
  };
};

/**
 * Middleware to check if user can view another user's profile
 */
export const checkProfileAccess = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const requestingUserId = parseInt(req.user?.id || '0', 10);
    const targetUserId = parseInt(req.params.id, 10);

    if (isNaN(targetUserId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // If requesting own profile, allow access
    if (requestingUserId === targetUserId) {
      return next();
    }

    // Check if target user exists and is not deleted
    const targetUser = await UserModel.findById(targetUserId);
    if (!targetUser || targetUser.is_deleted) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if profile is public
    const profile = await UserProfileModel.findByUserId(targetUserId);
    if (profile && !profile.is_public) {
      return res.status(403).json({ error: 'This profile is private' });
    }

    next();
  } catch (error) {
    logger.error('Error in profile access middleware:', error);
    next(error);
  }
};

/**
 * Middleware to validate user exists and is active
 */
export const validateUserExists = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = parseInt(req.params.id, 10);

    if (isNaN(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const user = await UserModel.findById(userId);
    if (!user || user.is_deleted) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (user.status === 'suspended') {
      return res.status(403).json({ error: 'User account is suspended' });
    }

    // Attach user to request for use in subsequent middleware/controllers
    req.targetUser = user;
    next();
  } catch (error) {
    logger.error('Error in user validation middleware:', error);
    next(error);
  }
};

/**
 * Middleware to check if user can perform admin actions
 */
export const requireAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const userId = parseInt(req.user?.id || '0', 10);

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Check if user has admin role
    const user = await UserModel.findById(userId);
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // This would need to be implemented with proper role checking
    // For now, we'll use a simple check
    if (req.user?.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    next();
  } catch (error) {
    logger.error('Error in admin middleware:', error);
    next(error);
  }
};

/**
 * Middleware to rate limit sensitive operations
 */
export const rateLimitSensitive = (maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) => {
  const attempts = new Map<string, { count: number; resetTime: number }>();

  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const userId = req.user?.id;
    const ip = req.ip;
    const key = userId ? `user:${userId}` : `ip:${ip}`;
    
    const now = Date.now();
    const userAttempts = attempts.get(key);

    if (userAttempts) {
      if (now > userAttempts.resetTime) {
        // Reset window
        attempts.set(key, { count: 1, resetTime: now + windowMs });
      } else if (userAttempts.count >= maxAttempts) {
        return res.status(429).json({ 
          error: 'Too many attempts. Please try again later.',
          retryAfter: Math.ceil((userAttempts.resetTime - now) / 1000)
        });
      } else {
        userAttempts.count++;
      }
    } else {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
    }

    next();
  };
};

/**
 * Middleware to validate request body for user updates
 */
export const validateUserUpdate = (req: Request, res: Response, next: NextFunction) => {
  const allowedFields = [
    'first_name', 'last_name', 'phone', 'date_of_birth'
  ];

  const updates = Object.keys(req.body);
  const isValidUpdate = updates.every(field => allowedFields.includes(field));

  if (!isValidUpdate) {
    const invalidFields = updates.filter(field => !allowedFields.includes(field));
    return res.status(400).json({ 
      error: 'Invalid update fields', 
      invalid_fields: invalidFields 
    });
  }

  // Validate specific fields
  if (req.body.date_of_birth) {
    const date = new Date(req.body.date_of_birth);
    if (isNaN(date.getTime())) {
      return res.status(400).json({ error: 'Invalid date format for date_of_birth' });
    }
    
    // Check if user is at least 13 years old
    const thirteenYearsAgo = new Date();
    thirteenYearsAgo.setFullYear(thirteenYearsAgo.getFullYear() - 13);
    
    if (date > thirteenYearsAgo) {
      return res.status(400).json({ error: 'User must be at least 13 years old' });
    }
  }

  if (req.body.phone && !/^\+?[\d\s\-\(\)]+$/.test(req.body.phone)) {
    return res.status(400).json({ error: 'Invalid phone number format' });
  }

  next();
};

/**
 * Middleware to validate profile update data
 */
export const validateProfileUpdate = (req: Request, res: Response, next: NextFunction) => {
  const allowedFields = [
    'bio', 'avatar_url', 'cover_image_url', 'location', 'timezone', 
    'language', 'social_links', 'preferences', 'notification_settings', 
    'privacy_settings', 'is_public'
  ];

  const updates = Object.keys(req.body);
  const isValidUpdate = updates.every(field => allowedFields.includes(field));

  if (!isValidUpdate) {
    const invalidFields = updates.filter(field => !allowedFields.includes(field));
    return res.status(400).json({ 
      error: 'Invalid update fields', 
      invalid_fields: invalidFields 
    });
  }

  // Validate bio length
  if (req.body.bio && req.body.bio.length > 500) {
    return res.status(400).json({ error: 'Bio must be 500 characters or less' });
  }

  // Validate URLs
  const urlFields = ['avatar_url', 'cover_image_url'];
  for (const field of urlFields) {
    if (req.body[field]) {
      try {
        new URL(req.body[field]);
      } catch {
        return res.status(400).json({ error: `Invalid URL format for ${field}` });
      }
    }
  }

  // Validate language code
  if (req.body.language && !/^[a-z]{2}(-[A-Z]{2})?$/.test(req.body.language)) {
    return res.status(400).json({ error: 'Invalid language code format' });
  }

  next();
};

/**
 * Middleware to sanitize user input
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeString = (str: string): string => {
    return str.trim().replace(/[<>]/g, '');
  };

  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return sanitizeString(obj);
    }
    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    return obj;
  };

  req.body = sanitizeObject(req.body);
  next();
};

// Extend Request interface to include targetUser
declare global {
  namespace Express {
    interface Request {
      targetUser?: any;
    }
  }
}
