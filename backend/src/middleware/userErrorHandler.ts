import { Request, Response, NextFunction } from 'express';
import { UserValidationError, UserProfileValidationError } from '@/models';
import { logger } from '@/utils/logger';

export interface UserError extends Error {
  statusCode?: number;
  field?: string;
  code?: string;
}

/**
 * Custom error classes for user operations
 */
export class UserNotFoundError extends Error {
  statusCode = 404;
  code = 'USER_NOT_FOUND';
  
  constructor(message = 'User not found') {
    super(message);
    this.name = 'UserNotFoundError';
  }
}

export class UserAccessDeniedError extends Error {
  statusCode = 403;
  code = 'ACCESS_DENIED';
  
  constructor(message = 'Access denied') {
    super(message);
    this.name = 'UserAccessDeniedError';
  }
}

export class UserAlreadyExistsError extends Error {
  statusCode = 409;
  code = 'USER_ALREADY_EXISTS';
  field?: string;
  
  constructor(message = 'User already exists', field?: string) {
    super(message);
    this.name = 'UserAlreadyExistsError';
    this.field = field;
  }
}

export class UserInactiveError extends Error {
  statusCode = 403;
  code = 'USER_INACTIVE';
  
  constructor(message = 'User account is inactive') {
    super(message);
    this.name = 'UserInactiveError';
  }
}

export class UserSuspendedError extends Error {
  statusCode = 403;
  code = 'USER_SUSPENDED';
  
  constructor(message = 'User account is suspended') {
    super(message);
    this.name = 'UserSuspendedError';
  }
}

export class FollowError extends Error {
  statusCode = 400;
  code = 'FOLLOW_ERROR';
  
  constructor(message: string) {
    super(message);
    this.name = 'FollowError';
  }
}

export class ProfilePrivateError extends Error {
  statusCode = 403;
  code = 'PROFILE_PRIVATE';
  
  constructor(message = 'This profile is private') {
    super(message);
    this.name = 'ProfilePrivateError';
  }
}

export class RateLimitError extends Error {
  statusCode = 429;
  code = 'RATE_LIMIT_EXCEEDED';
  retryAfter?: number;
  
  constructor(message = 'Rate limit exceeded', retryAfter?: number) {
    super(message);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

/**
 * User-specific error handler middleware
 */
export const userErrorHandler = (
  error: UserError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log the error
  logger.error('User operation error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    userId: req.user?.id,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Handle specific error types
  if (error instanceof UserValidationError) {
    return res.status(400).json({
      success: false,
      error: error.message,
      field: error.field,
      code: 'VALIDATION_ERROR'
    });
  }

  if (error instanceof UserProfileValidationError) {
    return res.status(400).json({
      success: false,
      error: error.message,
      field: error.field,
      code: 'PROFILE_VALIDATION_ERROR'
    });
  }

  if (error instanceof UserNotFoundError) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      code: error.code
    });
  }

  if (error instanceof UserAccessDeniedError) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      code: error.code
    });
  }

  if (error instanceof UserAlreadyExistsError) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      field: error.field,
      code: error.code
    });
  }

  if (error instanceof UserInactiveError || error instanceof UserSuspendedError) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      code: error.code
    });
  }

  if (error instanceof FollowError) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      code: error.code
    });
  }

  if (error instanceof ProfilePrivateError) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      code: error.code
    });
  }

  if (error instanceof RateLimitError) {
    const response: any = {
      success: false,
      error: error.message,
      code: error.code
    };
    
    if (error.retryAfter) {
      response.retryAfter = error.retryAfter;
      res.set('Retry-After', error.retryAfter.toString());
    }
    
    return res.status(error.statusCode).json(response);
  }

  // Handle database errors
  if (error.message.includes('duplicate key value')) {
    const field = error.message.includes('email') ? 'email' : 
                  error.message.includes('username') ? 'username' : 'unknown';
    
    return res.status(409).json({
      success: false,
      error: `${field.charAt(0).toUpperCase() + field.slice(1)} already exists`,
      field,
      code: 'DUPLICATE_VALUE'
    });
  }

  if (error.message.includes('foreign key constraint')) {
    return res.status(400).json({
      success: false,
      error: 'Invalid reference to related data',
      code: 'FOREIGN_KEY_ERROR'
    });
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      error: 'Invalid authentication token',
      code: 'INVALID_TOKEN'
    });
  }

  if (error.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      error: 'Authentication token has expired',
      code: 'TOKEN_EXPIRED'
    });
  }

  // Handle file upload errors
  if (error.message.includes('File too large')) {
    return res.status(413).json({
      success: false,
      error: 'File size exceeds the maximum allowed limit',
      code: 'FILE_TOO_LARGE'
    });
  }

  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      error: 'Invalid file type. Only images are allowed',
      code: 'INVALID_FILE_TYPE'
    });
  }

  // Handle network/timeout errors
  if (error.message.includes('timeout') || error.code === 'ETIMEDOUT') {
    return res.status(504).json({
      success: false,
      error: 'Request timeout. Please try again',
      code: 'REQUEST_TIMEOUT'
    });
  }

  // Handle generic errors with status codes
  if (error.statusCode) {
    return res.status(error.statusCode).json({
      success: false,
      error: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }

  // Default error response
  next(error);
};

/**
 * Async error wrapper for user controllers
 */
export const asyncUserHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Validation error formatter
 */
export const formatValidationErrors = (errors: string[]): string => {
  if (errors.length === 1) {
    return errors[0];
  }
  
  if (errors.length === 2) {
    return `${errors[0]} and ${errors[1]}`;
  }
  
  return `${errors.slice(0, -1).join(', ')}, and ${errors[errors.length - 1]}`;
};

/**
 * Create standardized error response
 */
export const createUserErrorResponse = (
  message: string,
  statusCode: number = 400,
  code?: string,
  field?: string,
  details?: any
) => {
  const response: any = {
    success: false,
    error: message
  };
  
  if (code) response.code = code;
  if (field) response.field = field;
  if (details) response.details = details;
  
  return { response, statusCode };
};

/**
 * Log user operation for audit trail
 */
export const logUserOperation = (
  req: Request,
  operation: string,
  success: boolean,
  details?: any
) => {
  logger.info('User operation logged:', {
    operation,
    success,
    userId: req.user?.id,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
    details
  });
};
