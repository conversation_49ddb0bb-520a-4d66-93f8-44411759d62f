import { db } from '@/config/database';
import { logger } from '@/utils/logger';

/**
 * Query optimization utilities for user-related operations
 */
export class QueryOptimizer {
  /**
   * Optimized user search with proper indexing
   */
  static buildOptimizedUserSearch(filters: any) {
    let query = db('users as u')
      .leftJoin('user_profiles as up', 'u.user_id', 'up.user_id')
      .where('u.is_deleted', false)
      .where('u.status', 'active');

    // Use indexed columns for better performance
    if (filters.query) {
      const searchTerm = `%${filters.query}%`;
      query = query.where(function() {
        // Username is indexed, so search it first
        this.where('u.username', 'ILIKE', searchTerm)
          .orWhere(function() {
            // Only search names if username doesn't match
            this.where('u.first_name', 'ILIKE', searchTerm)
              .orWhere('u.last_name', 'ILIKE', searchTerm);
          });
      });
    }

    // Location is indexed
    if (filters.location) {
      query = query.where('up.location', 'ILIKE', `%${filters.location}%`);
    }

    return query;
  }

  /**
   * Optimized follower/following queries with pagination
   */
  static buildOptimizedFollowQuery(
    userId: number,
    type: 'followers' | 'following',
    page: number = 1,
    limit: number = 20
  ) {
    const offset = (page - 1) * limit;
    const followTable = 'user_follows as uf';
    const userTable = 'users as u';
    const profileTable = 'user_profiles as up';

    let query = db(followTable)
      .join(userTable, 
        type === 'followers' ? 'uf.follower_id' : 'uf.following_id', 
        'u.user_id'
      )
      .leftJoin(profileTable, 'u.user_id', 'up.user_id')
      .where(
        type === 'followers' ? 'uf.following_id' : 'uf.follower_id', 
        userId
      )
      .where('uf.is_active', true)
      .where('u.is_deleted', false);

    // Select only necessary fields
    query = query.select(
      'u.user_id',
      'u.username',
      'u.first_name',
      'u.last_name',
      'up.avatar_url',
      'uf.followed_at'
    );

    return {
      dataQuery: query.clone()
        .orderBy('uf.followed_at', 'desc')
        .limit(limit)
        .offset(offset),
      countQuery: query.clone()
        .clearSelect()
        .clearOrder()
        .count('* as count')
        .first()
    };
  }

  /**
   * Optimized dashboard data aggregation
   */
  static async getOptimizedDashboardData(userId: number) {
    // Use Promise.all for parallel execution
    const [
      userStats,
      recentActivity,
      upcomingEvents,
      recentFollowers
    ] = await Promise.all([
      this.getOptimizedUserStats(userId),
      this.getOptimizedRecentActivity(userId, 10),
      this.getOptimizedUpcomingEvents(userId, 5),
      this.getOptimizedRecentFollowers(userId, 5)
    ]);

    return {
      userStats,
      recentActivity,
      upcomingEvents,
      recentFollowers
    };
  }

  /**
   * Optimized user statistics calculation
   */
  private static async getOptimizedUserStats(userId: number) {
    // Single query to get follow counts
    const followStats = await db.raw(`
      SELECT 
        (SELECT COUNT(*) FROM user_follows WHERE following_id = ? AND is_active = true) as followers_count,
        (SELECT COUNT(*) FROM user_follows WHERE follower_id = ? AND is_active = true) as following_count
    `, [userId, userId]);

    // Get event stats if user is an organizer
    const eventStats = await db.raw(`
      SELECT 
        COALESCE(COUNT(*), 0) as events_created
      FROM events e
      WHERE e.organizer_user_id = ? AND e.is_deleted = false
    `, [userId]);

    // Get ticket stats if user is a fan
    const ticketStats = await db.raw(`
      SELECT 
        COALESCE(COUNT(DISTINCT t.event_id), 0) as events_attended,
        COALESCE(COUNT(*), 0) as tickets_purchased,
        COALESCE(SUM(t.price_paid), 0) as total_spent
      FROM tickets t
      WHERE t.purchaser_user_id = ? AND t.is_deleted = false
    `, [userId]);

    return {
      followers_count: parseInt(followStats.rows[0].followers_count),
      following_count: parseInt(followStats.rows[0].following_count),
      events_created: parseInt(eventStats.rows[0].events_created),
      events_attended: parseInt(ticketStats.rows[0].events_attended),
      tickets_purchased: parseInt(ticketStats.rows[0].tickets_purchased),
      total_spent: parseFloat(ticketStats.rows[0].total_spent)
    };
  }

  /**
   * Optimized recent activity query
   */
  private static async getOptimizedRecentActivity(userId: number, limit: number) {
    return db('audit_logs')
      .where('user_id', userId)
      .select('action', 'resource_type', 'created_at', 'metadata')
      .orderBy('created_at', 'desc')
      .limit(limit);
  }

  /**
   * Optimized upcoming events query
   */
  private static async getOptimizedUpcomingEvents(userId: number, limit: number) {
    const now = new Date();

    // Get events user is attending or organizing
    return db.raw(`
      (
        SELECT e.*, 'attending' as user_relation
        FROM events e
        JOIN tickets t ON e.event_id = t.event_id
        WHERE t.purchaser_user_id = ?
        AND e.start_datetime > ?
        AND e.is_deleted = false
        AND t.is_deleted = false
        ORDER BY e.start_datetime ASC
        LIMIT ?
      )
      UNION ALL
      (
        SELECT e.*, 'organizing' as user_relation
        FROM events e
        WHERE e.organizer_user_id = ?
        AND e.start_datetime > ?
        AND e.is_deleted = false
        ORDER BY e.start_datetime ASC
        LIMIT ?
      )
      ORDER BY start_datetime ASC
      LIMIT ?
    `, [userId, now, limit, userId, now, limit, limit]);
  }

  /**
   * Optimized recent followers query
   */
  private static async getOptimizedRecentFollowers(userId: number, limit: number) {
    return db('user_follows as uf')
      .join('users as u', 'uf.follower_id', 'u.user_id')
      .leftJoin('user_profiles as up', 'u.user_id', 'up.user_id')
      .where('uf.following_id', userId)
      .where('uf.is_active', true)
      .where('u.is_deleted', false)
      .select(
        'u.user_id',
        'u.username',
        'u.first_name',
        'u.last_name',
        'up.avatar_url',
        'uf.followed_at'
      )
      .orderBy('uf.followed_at', 'desc')
      .limit(limit);
  }

  /**
   * Batch user data loading for better performance
   */
  static async batchLoadUsers(userIds: number[]) {
    if (userIds.length === 0) return [];

    return db('users as u')
      .leftJoin('user_profiles as up', 'u.user_id', 'up.user_id')
      .whereIn('u.user_id', userIds)
      .where('u.is_deleted', false)
      .select(
        'u.user_id',
        'u.username',
        'u.first_name',
        'u.last_name',
        'u.email',
        'u.status',
        'u.email_verified',
        'u.created_at',
        'up.avatar_url',
        'up.bio',
        'up.location'
      );
  }

  /**
   * Optimized analytics query with date range
   */
  static async getOptimizedAnalytics(
    userId: number,
    dateFrom: Date,
    dateTo: Date
  ) {
    // Activity by day
    const activityByDay = await db.raw(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM audit_logs
      WHERE user_id = ? AND created_at BETWEEN ? AND ?
      GROUP BY DATE(created_at)
      ORDER BY date
    `, [userId, dateFrom, dateTo]);

    // Activity by type
    const activityByType = await db('audit_logs')
      .where('user_id', userId)
      .whereBetween('created_at', [dateFrom, dateTo])
      .select('action')
      .count('* as count')
      .groupBy('action');

    // Growth metrics in a single query
    const growthMetrics = await db.raw(`
      SELECT 
        (SELECT COUNT(*) FROM user_follows 
         WHERE following_id = ? AND followed_at BETWEEN ? AND ? AND is_active = true) as new_followers,
        (SELECT COUNT(*) FROM user_follows 
         WHERE follower_id = ? AND followed_at BETWEEN ? AND ? AND is_active = true) as new_following,
        (SELECT COUNT(*) FROM events 
         WHERE organizer_user_id = ? AND created_at BETWEEN ? AND ? AND is_deleted = false) as events_created,
        (SELECT COUNT(*) FROM tickets 
         WHERE purchaser_user_id = ? AND created_at BETWEEN ? AND ? AND is_deleted = false) as tickets_purchased
    `, [
      userId, dateFrom, dateTo,
      userId, dateFrom, dateTo,
      userId, dateFrom, dateTo,
      userId, dateFrom, dateTo
    ]);

    return {
      activity_by_day: activityByDay.rows,
      activity_by_type: activityByType.reduce((acc: any, item: any) => {
        acc[item.action] = parseInt(item.count);
        return acc;
      }, {}),
      growth_metrics: {
        new_followers: parseInt(growthMetrics.rows[0].new_followers),
        new_following: parseInt(growthMetrics.rows[0].new_following),
        events_created: parseInt(growthMetrics.rows[0].events_created),
        tickets_purchased: parseInt(growthMetrics.rows[0].tickets_purchased)
      }
    };
  }

  /**
   * Query performance monitoring
   */
  static async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      
      if (duration > 1000) { // Log slow queries (>1s)
        logger.warn(`Slow query detected: ${queryName}`, {
          duration: `${duration}ms`,
          query: queryName
        });
      } else {
        logger.debug(`Query executed: ${queryName}`, {
          duration: `${duration}ms`
        });
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Query failed: ${queryName}`, {
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Database connection pool monitoring
   */
  static getConnectionPoolStats() {
    // This would depend on your database configuration
    // For Knex.js with PostgreSQL
    return {
      // pool: db.client.pool
    };
  }
}
