import { logger } from '@/utils/logger';

/**
 * Simple in-memory cache manager for user data
 * In production, this should be replaced with Redis or similar
 */
class CacheManager {
  private cache = new Map<string, { data: any; expiry: number }>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Set cache entry with TTL
   */
  set(key: string, data: any, ttl?: number): void {
    const expiry = Date.now() + (ttl || this.defaultTTL);
    this.cache.set(key, { data, expiry });
  }

  /**
   * Get cache entry
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Delete cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Clear expired entries
   */
  clearExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Cache wrapper for async functions
   */
  async cached<T>(
    key: string,
    fn: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get(key);
    if (cached !== null) {
      logger.debug(`Cache hit for key: ${key}`);
      return cached;
    }

    // Execute function and cache result
    logger.debug(`Cache miss for key: ${key}`);
    try {
      const result = await fn();
      this.set(key, result, ttl);
      return result;
    } catch (error) {
      logger.error(`Error executing cached function for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): number {
    let count = 0;
    const regex = new RegExp(pattern);
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }
    
    return count;
  }
}

// Create singleton instance
export const cacheManager = new CacheManager();

// Cache key generators for user data
export const CacheKeys = {
  user: (userId: number) => `user:${userId}`,
  userProfile: (userId: number) => `user:${userId}:profile`,
  userStats: (userId: number) => `user:${userId}:stats`,
  userFollowers: (userId: number, page: number = 1) => `user:${userId}:followers:${page}`,
  userFollowing: (userId: number, page: number = 1) => `user:${userId}:following:${page}`,
  userDashboard: (userId: number) => `user:${userId}:dashboard`,
  userActivity: (userId: number, page: number = 1) => `user:${userId}:activity:${page}`,
  userAnalytics: (userId: number, dateFrom?: string, dateTo?: string) => 
    `user:${userId}:analytics:${dateFrom || 'all'}:${dateTo || 'all'}`,
  userSearch: (query: string, page: number = 1) => `search:users:${query}:${page}`,
  userDiscovery: (userId: number) => `user:${userId}:discovery`,
  followStatus: (followerId: number, followingId: number) => 
    `follow:${followerId}:${followingId}`,
  trendingUsers: () => 'trending:users',
  activeUsers: () => 'active:users'
};

// Cache TTL constants (in milliseconds)
export const CacheTTL = {
  SHORT: 1 * 60 * 1000,      // 1 minute
  MEDIUM: 5 * 60 * 1000,     // 5 minutes
  LONG: 15 * 60 * 1000,      // 15 minutes
  HOUR: 60 * 60 * 1000,      // 1 hour
  DAY: 24 * 60 * 60 * 1000   // 24 hours
};

/**
 * Cache invalidation helpers
 */
export const CacheInvalidation = {
  /**
   * Invalidate all user-related cache entries
   */
  invalidateUser: (userId: number): number => {
    return cacheManager.invalidatePattern(`user:${userId}:`);
  },

  /**
   * Invalidate follow-related cache entries
   */
  invalidateFollows: (userId: number): number => {
    let count = 0;
    count += cacheManager.invalidatePattern(`user:${userId}:followers:`);
    count += cacheManager.invalidatePattern(`user:${userId}:following:`);
    count += cacheManager.invalidatePattern(`follow:${userId}:`);
    count += cacheManager.invalidatePattern(`follow:.*:${userId}`);
    return count;
  },

  /**
   * Invalidate search cache
   */
  invalidateSearch: (): number => {
    return cacheManager.invalidatePattern('search:');
  },

  /**
   * Invalidate trending/discovery cache
   */
  invalidateDiscovery: (): number => {
    let count = 0;
    count += cacheManager.invalidatePattern('trending:');
    count += cacheManager.invalidatePattern('active:');
    count += cacheManager.invalidatePattern('.*:discovery');
    return count;
  }
};

/**
 * Cached function decorators
 */
export const withCache = (
  keyGenerator: (...args: any[]) => string,
  ttl: number = CacheTTL.MEDIUM
) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator(...args);
      
      return cacheManager.cached(key, async () => {
        return method.apply(this, args);
      }, ttl);
    };

    return descriptor;
  };
};

/**
 * Performance monitoring for cached operations
 */
export class CachePerformanceMonitor {
  private static hits = 0;
  private static misses = 0;
  private static errors = 0;

  static recordHit(): void {
    this.hits++;
  }

  static recordMiss(): void {
    this.misses++;
  }

  static recordError(): void {
    this.errors++;
  }

  static getStats(): {
    hits: number;
    misses: number;
    errors: number;
    hitRate: number;
    total: number;
  } {
    const total = this.hits + this.misses;
    const hitRate = total > 0 ? (this.hits / total) * 100 : 0;

    return {
      hits: this.hits,
      misses: this.misses,
      errors: this.errors,
      hitRate: Math.round(hitRate * 100) / 100,
      total
    };
  }

  static reset(): void {
    this.hits = 0;
    this.misses = 0;
    this.errors = 0;
  }
}

// Cleanup expired cache entries every 10 minutes
setInterval(() => {
  cacheManager.clearExpired();
  logger.debug('Cache cleanup completed', cacheManager.getStats());
}, 10 * 60 * 1000);

// Log cache statistics every hour
setInterval(() => {
  const cacheStats = cacheManager.getStats();
  const perfStats = CachePerformanceMonitor.getStats();
  
  logger.info('Cache performance statistics:', {
    cache: cacheStats,
    performance: perfStats
  });
}, 60 * 60 * 1000);
