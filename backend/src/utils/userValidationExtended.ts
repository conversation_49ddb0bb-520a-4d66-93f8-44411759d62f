import { UserSearchFilters, UserPreferences, NotificationSettings, PrivacySettings } from '@/types/user';

export class UserValidationExtended {
  /**
   * Validate search filters
   */
  static validateSearchFilters(filters: UserSearchFilters): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate page and limit
    if (filters.page !== undefined) {
      if (!Number.isInteger(filters.page) || filters.page < 1) {
        errors.push('Page must be a positive integer');
      }
    }

    if (filters.limit !== undefined) {
      if (!Number.isInteger(filters.limit) || filters.limit < 1 || filters.limit > 100) {
        errors.push('Limit must be between 1 and 100');
      }
    }

    // Validate query length
    if (filters.query !== undefined) {
      if (typeof filters.query !== 'string') {
        errors.push('Query must be a string');
      } else if (filters.query.length > 100) {
        errors.push('Query must be 100 characters or less');
      }
    }

    // Validate location
    if (filters.location !== undefined) {
      if (typeof filters.location !== 'string') {
        errors.push('Location must be a string');
      } else if (filters.location.length > 255) {
        errors.push('Location must be 255 characters or less');
      }
    }

    // Validate sort options
    if (filters.sort_by !== undefined) {
      const validSortBy = ['relevance', 'followers', 'activity', 'created_at'];
      if (!validSortBy.includes(filters.sort_by)) {
        errors.push(`Sort by must be one of: ${validSortBy.join(', ')}`);
      }
    }

    if (filters.sort_order !== undefined) {
      const validSortOrder = ['asc', 'desc'];
      if (!validSortOrder.includes(filters.sort_order)) {
        errors.push(`Sort order must be one of: ${validSortOrder.join(', ')}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate user preferences
   */
  static validateUserPreferences(preferences: UserPreferences): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (preferences.theme !== undefined) {
      const validThemes = ['light', 'dark', 'auto'];
      if (!validThemes.includes(preferences.theme)) {
        errors.push(`Theme must be one of: ${validThemes.join(', ')}`);
      }
    }

    if (preferences.language !== undefined) {
      if (typeof preferences.language !== 'string' || !/^[a-z]{2}(-[A-Z]{2})?$/.test(preferences.language)) {
        errors.push('Language must be a valid language code (e.g., en, en-US)');
      }
    }

    if (preferences.timezone !== undefined) {
      if (typeof preferences.timezone !== 'string' || preferences.timezone.length > 50) {
        errors.push('Timezone must be a valid timezone string');
      }
    }

    if (preferences.date_format !== undefined) {
      const validFormats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'];
      if (!validFormats.includes(preferences.date_format)) {
        errors.push(`Date format must be one of: ${validFormats.join(', ')}`);
      }
    }

    if (preferences.time_format !== undefined) {
      const validFormats = ['12h', '24h'];
      if (!validFormats.includes(preferences.time_format)) {
        errors.push(`Time format must be one of: ${validFormats.join(', ')}`);
      }
    }

    if (preferences.currency !== undefined) {
      if (typeof preferences.currency !== 'string' || !/^[A-Z]{3}$/.test(preferences.currency)) {
        errors.push('Currency must be a valid 3-letter currency code (e.g., USD, EUR)');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate notification settings
   */
  static validateNotificationSettings(settings: NotificationSettings): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    const booleanFields = [
      'email_notifications',
      'push_notifications',
      'event_reminders',
      'marketing_emails',
      'weekly_digest'
    ];

    for (const field of booleanFields) {
      if (settings[field as keyof NotificationSettings] !== undefined) {
        if (typeof settings[field as keyof NotificationSettings] !== 'boolean') {
          errors.push(`${field} must be a boolean value`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate privacy settings
   */
  static validatePrivacySettings(settings: PrivacySettings): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (settings.profile_visibility !== undefined) {
      const validVisibility = ['public', 'private', 'friends_only'];
      if (!validVisibility.includes(settings.profile_visibility)) {
        errors.push(`Profile visibility must be one of: ${validVisibility.join(', ')}`);
      }
    }

    const booleanFields = [
      'show_email',
      'show_phone',
      'show_location',
      'allow_messages',
      'show_activity'
    ];

    for (const field of booleanFields) {
      if (settings[field as keyof PrivacySettings] !== undefined) {
        if (typeof settings[field as keyof PrivacySettings] !== 'boolean') {
          errors.push(`${field} must be a boolean value`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate social links
   */
  static validateSocialLinks(socialLinks: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (typeof socialLinks !== 'object' || socialLinks === null) {
      return { isValid: false, errors: ['Social links must be an object'] };
    }

    const validFields = ['twitter_handle', 'instagram_handle', 'linkedin_handle', 'website_url'];
    
    for (const [key, value] of Object.entries(socialLinks)) {
      if (!validFields.includes(key)) {
        errors.push(`Invalid social link field: ${key}`);
        continue;
      }

      if (value !== null && value !== undefined) {
        if (typeof value !== 'string') {
          errors.push(`${key} must be a string`);
          continue;
        }

        if (key === 'website_url') {
          try {
            new URL(value as string);
          } catch {
            errors.push(`${key} must be a valid URL`);
          }
        } else {
          // Validate social media handles
          if ((value as string).length > 50) {
            errors.push(`${key} must be 50 characters or less`);
          }
          
          if (!/^[a-zA-Z0-9_.-]+$/.test(value as string)) {
            errors.push(`${key} contains invalid characters`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate password strength
   */
  static validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      errors.push('Password must be 128 characters or less');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common weak passwords
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common and easily guessable');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize and validate bio text
   */
  static validateBio(bio: string): { isValid: boolean; errors: string[]; sanitized: string } {
    const errors: string[] = [];

    if (typeof bio !== 'string') {
      return { isValid: false, errors: ['Bio must be a string'], sanitized: '' };
    }

    // Sanitize HTML and trim
    const sanitized = bio
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[<>]/g, '') // Remove angle brackets
      .trim();

    if (sanitized.length > 500) {
      errors.push('Bio must be 500 characters or less');
    }

    // Check for inappropriate content (basic check)
    const inappropriateWords = ['spam', 'scam', 'fake'];
    const lowerBio = sanitized.toLowerCase();
    
    for (const word of inappropriateWords) {
      if (lowerBio.includes(word)) {
        errors.push('Bio contains inappropriate content');
        break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized
    };
  }

  /**
   * Validate file upload (for avatar/cover images)
   */
  static validateImageUpload(file: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!file) {
      return { isValid: false, errors: ['No file provided'] };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push('File size must be 5MB or less');
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      errors.push('File must be a valid image (JPEG, PNG, GIF, or WebP)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
