import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('user_follows', (table) => {
    table.increments('follow_id').primary();
    table.integer('follower_id').unsigned().notNullable().references('user_id').inTable('users').onDelete('CASCADE');
    table.integer('following_id').unsigned().notNullable().references('user_id').inTable('users').onDelete('CASCADE');
    table.timestamp('followed_at').defaultTo(knex.fn.now());
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);

    // Indexes
    table.index(['follower_id']);
    table.index(['following_id']);
    table.index(['follower_id', 'following_id']);
    table.index(['is_active']);
    
    // Unique constraint to prevent duplicate follows
    table.unique(['follower_id', 'following_id']);
    
    // Prevent self-following
    table.check('follower_id != following_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('user_follows');
}
