import request from 'supertest';
import jwt from 'jsonwebtoken';
import app from '@/index';
import { db } from '@/config/database';
import { UserModel, UserProfileModel, UserFollowModel } from '@/models';
import { config } from '@/config/environment';

describe('UserController', () => {
  let testUser: any;
  let testUser2: any;
  let authToken: string;
  let authToken2: string;

  beforeAll(async () => {
    // Create test users
    testUser = await UserModel.create({
      username: 'testuser1',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Test',
      last_name: 'User'
    });

    testUser2 = await UserModel.create({
      username: 'testuser2',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Test',
      last_name: 'User2'
    });

    // Generate auth tokens
    authToken = jwt.sign(
      { id: testUser.user_id.toString(), email: testUser.email, role: 'user' },
      config.jwt.secret,
      { expiresIn: '1h' }
    );

    authToken2 = jwt.sign(
      { id: testUser2.user_id.toString(), email: testUser2.email, role: 'user' },
      config.jwt.secret,
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Clean up test data
    await db('user_follows').where('follower_id', testUser.user_id).del();
    await db('user_follows').where('following_id', testUser.user_id).del();
    await db('user_profiles').where('user_id', testUser.user_id).del();
    await db('user_profiles').where('user_id', testUser2.user_id).del();
    await db('audit_logs').where('user_id', testUser.user_id).del();
    await db('audit_logs').where('user_id', testUser2.user_id).del();
    await UserModel.delete(testUser.user_id);
    await UserModel.delete(testUser2.user_id);
  });

  describe('GET /api/users/me', () => {
    it('should return current user data', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user_id).toBe(testUser.user_id);
      expect(response.body.data.username).toBe(testUser.username);
      expect(response.body.data).not.toHaveProperty('password_hash');
    });

    it('should return 401 without auth token', async () => {
      await request(app)
        .get('/api/users/me')
        .expect(401);
    });
  });

  describe('PUT /api/users/me', () => {
    it('should update current user data', async () => {
      const updateData = {
        first_name: 'Updated',
        last_name: 'Name'
      };

      const response = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.first_name).toBe('Updated');
      expect(response.body.data.last_name).toBe('Name');
    });

    it('should validate input data', async () => {
      const invalidData = {
        email: 'invalid-email',
        date_of_birth: 'invalid-date'
      };

      await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('GET /api/users/:id', () => {
    it('should return public user profile', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser2.user_id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user_id).toBe(testUser2.user_id);
      expect(response.body.data.username).toBe(testUser2.username);
      expect(response.body.data).not.toHaveProperty('email');
      expect(response.body.data).not.toHaveProperty('password_hash');
    });

    it('should return 404 for non-existent user', async () => {
      await request(app)
        .get('/api/users/99999')
        .expect(404);
    });
  });

  describe('PUT /api/users/me/profile', () => {
    it('should create/update user profile', async () => {
      const profileData = {
        bio: 'Test bio',
        location: 'Test City',
        timezone: 'UTC',
        is_public: true
      };

      const response = await request(app)
        .put('/api/users/me/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(profileData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.bio).toBe('Test bio');
      expect(response.body.data.location).toBe('Test City');
    });

    it('should validate profile data', async () => {
      const invalidData = {
        bio: 'x'.repeat(501), // Too long
        avatar_url: 'not-a-url'
      };

      await request(app)
        .put('/api/users/me/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('POST /api/users/:id/follow', () => {
    it('should follow another user', async () => {
      const response = await request(app)
        .post(`/api/users/${testUser2.user_id}/follow`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User followed successfully');

      // Verify follow relationship exists
      const isFollowing = await UserFollowModel.isFollowing(testUser.user_id, testUser2.user_id);
      expect(isFollowing).toBe(true);
    });

    it('should not allow following self', async () => {
      await request(app)
        .post(`/api/users/${testUser.user_id}/follow`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should not allow duplicate follows', async () => {
      // First follow should succeed
      await request(app)
        .post(`/api/users/${testUser2.user_id}/follow`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Second follow should fail
      await request(app)
        .post(`/api/users/${testUser2.user_id}/follow`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });
  });

  describe('DELETE /api/users/:id/follow', () => {
    beforeEach(async () => {
      // Ensure follow relationship exists
      await UserFollowModel.followUser(testUser.user_id, testUser2.user_id);
    });

    it('should unfollow a user', async () => {
      const response = await request(app)
        .delete(`/api/users/${testUser2.user_id}/follow`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User unfollowed successfully');

      // Verify follow relationship no longer exists
      const isFollowing = await UserFollowModel.isFollowing(testUser.user_id, testUser2.user_id);
      expect(isFollowing).toBe(false);
    });
  });

  describe('GET /api/users/:id/followers', () => {
    beforeEach(async () => {
      // Create follow relationship
      await UserFollowModel.followUser(testUser.user_id, testUser2.user_id);
    });

    it('should return user followers', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser2.user_id}/followers`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toBeDefined();
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser2.user_id}/followers?page=1&limit=10`)
        .expect(200);

      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(10);
    });
  });

  describe('GET /api/users/:id/following', () => {
    beforeEach(async () => {
      // Create follow relationship
      await UserFollowModel.followUser(testUser.user_id, testUser2.user_id);
    });

    it('should return users being followed', async () => {
      const response = await request(app)
        .get(`/api/users/${testUser.user_id}/following`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toBeDefined();
    });
  });

  describe('GET /api/users/search', () => {
    it('should search users by query', async () => {
      const response = await request(app)
        .get('/api/users/search?q=testuser')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toBeDefined();
    });

    it('should support search filters', async () => {
      const response = await request(app)
        .get('/api/users/search?q=test&limit=5&sort_by=username')
        .expect(200);

      expect(response.body.pagination.limit).toBe(5);
    });
  });

  describe('GET /api/users/me/dashboard', () => {
    it('should return dashboard data', async () => {
      const response = await request(app)
        .get('/api/users/me/dashboard')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('stats');
      expect(response.body.data).toHaveProperty('recent_activity');
    });
  });

  describe('GET /api/users/me/activity', () => {
    it('should return user activity history', async () => {
      const response = await request(app)
        .get('/api/users/me/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should support activity filters', async () => {
      const response = await request(app)
        .get('/api/users/me/activity?action=user_updated&limit=5')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
    });
  });

  describe('PUT /api/users/me/settings', () => {
    it('should update user settings', async () => {
      const settingsData = {
        preferences: {
          theme: 'dark',
          language: 'en'
        },
        notification_settings: {
          email_notifications: true,
          push_notifications: false
        }
      };

      const response = await request(app)
        .put('/api/users/me/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(settingsData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.preferences.theme).toBe('dark');
    });
  });

  describe('POST /api/users/me/deactivate', () => {
    it('should deactivate user account', async () => {
      const response = await request(app)
        .post('/api/users/me/deactivate')
        .set('Authorization', `Bearer ${authToken2}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Account deactivated successfully');

      // Verify user status is inactive
      const user = await UserModel.findById(testUser2.user_id);
      expect(user?.status).toBe('inactive');
    });
  });

  describe('Error handling', () => {
    it('should handle invalid user IDs', async () => {
      await request(app)
        .get('/api/users/invalid-id')
        .expect(400);
    });

    it('should handle unauthorized access', async () => {
      await request(app)
        .put('/api/users/me/profile')
        .send({ bio: 'test' })
        .expect(401);
    });

    it('should handle rate limiting on sensitive operations', async () => {
      // This would require multiple rapid requests to test rate limiting
      // Implementation depends on your rate limiting configuration
    });
  });
});
