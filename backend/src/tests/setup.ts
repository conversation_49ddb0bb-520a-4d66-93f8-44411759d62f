import { db } from '@/config/database';
import { logger } from '@/utils/logger';

// Set test environment
process.env['NODE_ENV'] = 'test';

// Increase timeout for database operations
jest.setTimeout(30000);

// Test database setup and teardown
beforeAll(async () => {
  try {
    // Ensure test database is clean
    await cleanTestDatabase();
    logger.info('Test database setup completed');
  } catch (error) {
    logger.error('Test database setup failed:', error);
    throw error;
  }
});

afterAll(async () => {
  try {
    // Clean up test database
    await cleanTestDatabase();

    // Close database connection
    await db.destroy();
    logger.info('Test database cleanup completed');
  } catch (error) {
    logger.error('Test database cleanup failed:', error);
  }
});

/**
 * Clean test database by removing test data
 */
async function cleanTestDatabase() {
  // List of tables to clean in dependency order
  const tablesToClean = [
    'audit_logs',
    'user_follows',
    'user_profiles',
    'tickets',
    'events',
    'event_tag_assignments',
    'event_tags',
    'event_categories',
    'user_role_assignments',
    'user_roles',
    'users'
  ];

  for (const table of tablesToClean) {
    try {
      // Only delete test data (identified by email domain or username pattern)
      if (table === 'users') {
        await db(table)
          .where('email', 'like', '%@example.com')
          .orWhere('username', 'like', '%test%')
          .del();
      } else if (table === 'audit_logs') {
        // Delete audit logs for test users
        await db(table)
          .whereIn('user_id', function() {
            this.select('user_id')
              .from('users')
              .where('email', 'like', '%@example.com')
              .orWhere('username', 'like', '%test%');
          })
          .del();
      } else {
        // For other tables, we'll clean them based on user relationships
        const hasUserIdColumn = await db.schema.hasColumn(table, 'user_id');
        if (hasUserIdColumn) {
          await db(table)
            .whereIn('user_id', function() {
              this.select('user_id')
                .from('users')
                .where('email', 'like', '%@example.com')
                .orWhere('username', 'like', '%test%');
            })
            .del();
        }
      }
    } catch (error) {
      // Some tables might not exist in test environment, that's okay
      logger.debug(`Could not clean table ${table}:`, error);
    }
  }
}

/**
 * Create test user helper
 */
export async function createTestUser(overrides: any = {}) {
  const defaultUser = {
    username: `testuser_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    email: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}@example.com`,
    password: 'TestPassword123!',
    first_name: 'Test',
    last_name: 'User',
    ...overrides
  };

  const { UserModel } = await import('@/models/User');
  return UserModel.create(defaultUser);
}

/**
 * Generate JWT token for testing
 */
export function generateTestToken(userId: number, email: string, role: string = 'user') {
  const jwt = require('jsonwebtoken');
  const { config } = require('@/config/environment');

  return jwt.sign(
    { id: userId.toString(), email, role },
    config.jwt.secret,
    { expiresIn: '1h' }
  );
}

// Suppress console logs during tests unless explicitly needed
if (process.env.NODE_ENV === 'test') {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
}
