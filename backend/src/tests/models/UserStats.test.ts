import { UserStatsModel } from '@/models/UserStats';
import { UserModel } from '@/models/User';
import { UserFollowModel } from '@/models/UserFollow';
import { UserActivityModel } from '@/models/UserActivity';
import { db } from '@/config/database';

describe('UserStatsModel', () => {
  let testUser: any;
  let testUser2: any;

  beforeAll(async () => {
    // Create test users
    testUser = await UserModel.create({
      username: 'statstest1',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Stats',
      last_name: 'Test1'
    });

    testUser2 = await UserModel.create({
      username: 'statstest2',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Stats',
      last_name: 'Test2'
    });
  });

  afterAll(async () => {
    // Clean up test data
    await db('user_follows').whereIn('follower_id', [testUser.user_id, testUser2.user_id]).del();
    await db('user_follows').whereIn('following_id', [testUser.user_id, testUser2.user_id]).del();
    await db('audit_logs').whereIn('user_id', [testUser.user_id, testUser2.user_id]).del();
    await UserModel.delete(testUser.user_id);
    await UserModel.delete(testUser2.user_id);
  });

  beforeEach(async () => {
    // Clean up data before each test
    await db('user_follows').whereIn('follower_id', [testUser.user_id, testUser2.user_id]).del();
    await db('user_follows').whereIn('following_id', [testUser.user_id, testUser2.user_id]).del();
    await db('audit_logs').whereIn('user_id', [testUser.user_id, testUser2.user_id]).del();
  });

  describe('getUserStats', () => {
    it('should return basic user statistics', async () => {
      const stats = await UserStatsModel.getUserStats(testUser.user_id);

      expect(stats).toHaveProperty('followers_count');
      expect(stats).toHaveProperty('following_count');
      expect(stats).toHaveProperty('events_created');
      expect(stats).toHaveProperty('events_attended');
      expect(stats).toHaveProperty('tickets_purchased');
      expect(stats).toHaveProperty('total_spent');
      expect(stats).toHaveProperty('profile_views');
      expect(stats).toHaveProperty('activity_score');

      // For new user, most stats should be 0
      expect(stats.followers_count).toBe(0);
      expect(stats.following_count).toBe(0);
      expect(stats.events_created).toBe(0);
      expect(stats.events_attended).toBe(0);
      expect(stats.tickets_purchased).toBe(0);
      expect(stats.total_spent).toBe(0);
    });

    it('should calculate follow counts correctly', async () => {
      // Create follow relationships
      await UserFollowModel.followUser(testUser2.user_id, testUser.user_id);
      
      const stats = await UserStatsModel.getUserStats(testUser.user_id);
      
      expect(stats.followers_count).toBe(1);
      expect(stats.following_count).toBe(0);
    });

    it('should calculate activity score based on recent activity', async () => {
      // Add some recent activity
      await UserActivityModel.logActivity(testUser.user_id, 'profile_updated');
      await UserActivityModel.logActivity(testUser.user_id, 'user_login');
      
      const stats = await UserStatsModel.getUserStats(testUser.user_id);
      
      expect(stats.activity_score).toBeGreaterThan(0);
      expect(stats.activity_score).toBeLessThanOrEqual(100);
    });
  });

  describe('getUserAnalytics', () => {
    beforeEach(async () => {
      // Add some test activity data
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);

      await UserActivityModel.logActivity(testUser.user_id, 'profile_updated', {
        metadata: { test: true }
      });
      
      // Manually insert activity with specific dates for testing
      await db('audit_logs').insert([
        {
          user_id: testUser.user_id,
          action: 'user_login',
          resource_type: 'user',
          created_at: yesterday
        },
        {
          user_id: testUser.user_id,
          action: 'profile_view',
          resource_type: 'user',
          created_at: twoDaysAgo
        }
      ]);
    });

    it('should return analytics for date range', async () => {
      const dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      const dateTo = new Date();

      const analytics = await UserStatsModel.getUserAnalytics(testUser.user_id, dateFrom, dateTo);

      expect(analytics).toHaveProperty('activity_by_day');
      expect(analytics).toHaveProperty('activity_by_type');
      expect(analytics).toHaveProperty('growth_metrics');

      expect(analytics.activity_by_day).toBeInstanceOf(Array);
      expect(analytics.activity_by_type).toBeInstanceOf(Object);
      expect(analytics.growth_metrics).toHaveProperty('new_followers');
      expect(analytics.growth_metrics).toHaveProperty('new_following');
      expect(analytics.growth_metrics).toHaveProperty('events_created');
      expect(analytics.growth_metrics).toHaveProperty('tickets_purchased');
    });

    it('should group activity by type correctly', async () => {
      const dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const dateTo = new Date();

      const analytics = await UserStatsModel.getUserAnalytics(testUser.user_id, dateFrom, dateTo);

      expect(analytics.activity_by_type).toHaveProperty('profile_updated');
      expect(analytics.activity_by_type).toHaveProperty('user_login');
      expect(analytics.activity_by_type).toHaveProperty('profile_view');
      
      expect(analytics.activity_by_type.profile_updated).toBeGreaterThan(0);
      expect(analytics.activity_by_type.user_login).toBeGreaterThan(0);
      expect(analytics.activity_by_type.profile_view).toBeGreaterThan(0);
    });

    it('should calculate growth metrics for date range', async () => {
      const dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const dateTo = new Date();

      // Add a follow relationship within the date range
      await UserFollowModel.followUser(testUser2.user_id, testUser.user_id);

      const analytics = await UserStatsModel.getUserAnalytics(testUser.user_id, dateFrom, dateTo);

      expect(analytics.growth_metrics.new_followers).toBe(1);
      expect(analytics.growth_metrics.new_following).toBe(0);
      expect(analytics.growth_metrics.events_created).toBe(0);
      expect(analytics.growth_metrics.tickets_purchased).toBe(0);
    });
  });

  describe('getActiveUsersLeaderboard', () => {
    beforeEach(async () => {
      // Add activity for both test users
      await UserActivityModel.logActivity(testUser.user_id, 'profile_updated');
      await UserActivityModel.logActivity(testUser.user_id, 'user_login');
      await UserActivityModel.logActivity(testUser2.user_id, 'profile_updated');
      
      // Add follow relationships to affect activity scores
      await UserFollowModel.followUser(testUser2.user_id, testUser.user_id);
    });

    it('should return leaderboard of active users', async () => {
      const leaderboard = await UserStatsModel.getActiveUsersLeaderboard(10);

      expect(leaderboard).toBeInstanceOf(Array);
      expect(leaderboard.length).toBeGreaterThan(0);
      
      // Each entry should have required properties
      leaderboard.forEach(entry => {
        expect(entry).toHaveProperty('user_id');
        expect(entry).toHaveProperty('username');
        expect(entry).toHaveProperty('activity_score');
        expect(entry).toHaveProperty('followers_count');
        expect(typeof entry.activity_score).toBe('number');
        expect(typeof entry.followers_count).toBe('number');
      });
    });

    it('should sort users by activity score descending', async () => {
      const leaderboard = await UserStatsModel.getActiveUsersLeaderboard(10);

      if (leaderboard.length > 1) {
        for (let i = 0; i < leaderboard.length - 1; i++) {
          expect(leaderboard[i].activity_score).toBeGreaterThanOrEqual(
            leaderboard[i + 1].activity_score
          );
        }
      }
    });

    it('should respect limit parameter', async () => {
      const leaderboard = await UserStatsModel.getActiveUsersLeaderboard(1);
      expect(leaderboard.length).toBeLessThanOrEqual(1);
    });
  });

  describe('Activity Score Calculation', () => {
    it('should calculate higher scores for more active users', async () => {
      // Add more activity for testUser
      for (let i = 0; i < 10; i++) {
        await UserActivityModel.logActivity(testUser.user_id, 'profile_updated');
      }

      // Add followers for testUser
      await UserFollowModel.followUser(testUser2.user_id, testUser.user_id);

      // Add minimal activity for testUser2
      await UserActivityModel.logActivity(testUser2.user_id, 'user_login');

      const stats1 = await UserStatsModel.getUserStats(testUser.user_id);
      const stats2 = await UserStatsModel.getUserStats(testUser2.user_id);

      expect(stats1.activity_score).toBeGreaterThan(stats2.activity_score);
    });

    it('should cap activity score at 100', async () => {
      // Add excessive activity
      for (let i = 0; i < 100; i++) {
        await UserActivityModel.logActivity(testUser.user_id, 'profile_updated');
      }

      // Add many followers
      for (let i = 0; i < 50; i++) {
        const follower = await UserModel.create({
          username: `follower${i}`,
          email: `follower${i}@example.com`,
          password: 'TestPassword123!',
          first_name: 'Follower',
          last_name: `${i}`
        });
        
        await UserFollowModel.followUser(follower.user_id, testUser.user_id);
        
        // Clean up immediately to avoid affecting other tests
        await db('user_follows').where('follower_id', follower.user_id).del();
        await UserModel.delete(follower.user_id);
      }

      const stats = await UserStatsModel.getUserStats(testUser.user_id);
      expect(stats.activity_score).toBeLessThanOrEqual(100);
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent user gracefully', async () => {
      const stats = await UserStatsModel.getUserStats(99999);
      
      expect(stats.followers_count).toBe(0);
      expect(stats.following_count).toBe(0);
      expect(stats.events_created).toBe(0);
      expect(stats.events_attended).toBe(0);
      expect(stats.tickets_purchased).toBe(0);
      expect(stats.total_spent).toBe(0);
    });

    it('should handle analytics for user with no activity', async () => {
      const dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const dateTo = new Date();

      const analytics = await UserStatsModel.getUserAnalytics(99999, dateFrom, dateTo);

      expect(analytics.activity_by_day).toEqual([]);
      expect(analytics.activity_by_type).toEqual({});
      expect(analytics.growth_metrics.new_followers).toBe(0);
      expect(analytics.growth_metrics.new_following).toBe(0);
      expect(analytics.growth_metrics.events_created).toBe(0);
      expect(analytics.growth_metrics.tickets_purchased).toBe(0);
    });
  });
});
