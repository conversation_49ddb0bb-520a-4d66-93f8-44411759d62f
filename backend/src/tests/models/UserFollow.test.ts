import { UserFollowModel } from '@/models/UserFollow';
import { UserModel } from '@/models/User';
import { db } from '@/config/database';

describe('UserFollowModel', () => {
  let testUser1: any;
  let testUser2: any;
  let testUser3: any;

  beforeAll(async () => {
    // Create test users
    testUser1 = await UserModel.create({
      username: 'followtest1',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Follow',
      last_name: 'Test1'
    });

    testUser2 = await UserModel.create({
      username: 'followtest2',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Follow',
      last_name: 'Test2'
    });

    testUser3 = await UserModel.create({
      username: 'followtest3',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      first_name: 'Follow',
      last_name: 'Test3'
    });
  });

  afterAll(async () => {
    // Clean up test data
    await db('user_follows').whereIn('follower_id', [testUser1.user_id, testUser2.user_id, testUser3.user_id]).del();
    await db('user_follows').whereIn('following_id', [testUser1.user_id, testUser2.user_id, testUser3.user_id]).del();
    await UserModel.delete(testUser1.user_id);
    await UserModel.delete(testUser2.user_id);
    await UserModel.delete(testUser3.user_id);
  });

  beforeEach(async () => {
    // Clean up follows before each test
    await db('user_follows').whereIn('follower_id', [testUser1.user_id, testUser2.user_id, testUser3.user_id]).del();
  });

  describe('followUser', () => {
    it('should create a follow relationship', async () => {
      const follow = await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);

      expect(follow).toBeDefined();
      expect(follow.follower_id).toBe(testUser1.user_id);
      expect(follow.following_id).toBe(testUser2.user_id);
      expect(follow.is_active).toBe(true);
    });

    it('should prevent self-following', async () => {
      await expect(
        UserFollowModel.followUser(testUser1.user_id, testUser1.user_id)
      ).rejects.toThrow('Users cannot follow themselves');
    });

    it('should prevent duplicate follows', async () => {
      // First follow should succeed
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);

      // Second follow should fail
      await expect(
        UserFollowModel.followUser(testUser1.user_id, testUser2.user_id)
      ).rejects.toThrow('Already following this user');
    });

    it('should reactivate inactive follow relationship', async () => {
      // Create and then deactivate a follow
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
      await UserFollowModel.unfollowUser(testUser1.user_id, testUser2.user_id);

      // Following again should reactivate
      const follow = await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
      expect(follow.is_active).toBe(true);
    });

    it('should throw error for non-existent users', async () => {
      await expect(
        UserFollowModel.followUser(99999, testUser2.user_id)
      ).rejects.toThrow('Follower user not found');

      await expect(
        UserFollowModel.followUser(testUser1.user_id, 99999)
      ).rejects.toThrow('User to follow not found');
    });
  });

  describe('unfollowUser', () => {
    beforeEach(async () => {
      // Create follow relationship
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
    });

    it('should deactivate follow relationship', async () => {
      const result = await UserFollowModel.unfollowUser(testUser1.user_id, testUser2.user_id);
      expect(result).toBe(true);

      const isFollowing = await UserFollowModel.isFollowing(testUser1.user_id, testUser2.user_id);
      expect(isFollowing).toBe(false);
    });

    it('should return false for non-existent follow', async () => {
      const result = await UserFollowModel.unfollowUser(testUser2.user_id, testUser3.user_id);
      expect(result).toBe(false);
    });
  });

  describe('isFollowing', () => {
    it('should return true for active follow relationship', async () => {
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
      
      const isFollowing = await UserFollowModel.isFollowing(testUser1.user_id, testUser2.user_id);
      expect(isFollowing).toBe(true);
    });

    it('should return false for no follow relationship', async () => {
      const isFollowing = await UserFollowModel.isFollowing(testUser1.user_id, testUser2.user_id);
      expect(isFollowing).toBe(false);
    });

    it('should return false for inactive follow relationship', async () => {
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
      await UserFollowModel.unfollowUser(testUser1.user_id, testUser2.user_id);
      
      const isFollowing = await UserFollowModel.isFollowing(testUser1.user_id, testUser2.user_id);
      expect(isFollowing).toBe(false);
    });
  });

  describe('getFollowers', () => {
    beforeEach(async () => {
      // Create multiple follow relationships
      await UserFollowModel.followUser(testUser1.user_id, testUser3.user_id);
      await UserFollowModel.followUser(testUser2.user_id, testUser3.user_id);
    });

    it('should return followers with pagination', async () => {
      const result = await UserFollowModel.getFollowers(testUser3.user_id, { page: 1, limit: 10 });

      expect(result.data).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      
      const followerIds = result.data.map(f => f.user_id);
      expect(followerIds).toContain(testUser1.user_id);
      expect(followerIds).toContain(testUser2.user_id);
    });

    it('should return empty array for user with no followers', async () => {
      const result = await UserFollowModel.getFollowers(testUser1.user_id);
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('getFollowing', () => {
    beforeEach(async () => {
      // Create multiple follow relationships
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
      await UserFollowModel.followUser(testUser1.user_id, testUser3.user_id);
    });

    it('should return following with pagination', async () => {
      const result = await UserFollowModel.getFollowing(testUser1.user_id, { page: 1, limit: 10 });

      expect(result.data).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      
      const followingIds = result.data.map(f => f.user_id);
      expect(followingIds).toContain(testUser2.user_id);
      expect(followingIds).toContain(testUser3.user_id);
    });

    it('should return empty array for user following no one', async () => {
      const result = await UserFollowModel.getFollowing(testUser2.user_id);
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('getFollowCounts', () => {
    beforeEach(async () => {
      // Create follow relationships
      await UserFollowModel.followUser(testUser1.user_id, testUser2.user_id);
      await UserFollowModel.followUser(testUser3.user_id, testUser2.user_id);
      await UserFollowModel.followUser(testUser2.user_id, testUser3.user_id);
    });

    it('should return correct follow counts', async () => {
      const counts = await UserFollowModel.getFollowCounts(testUser2.user_id);

      expect(counts.followers).toBe(2); // testUser1 and testUser3 follow testUser2
      expect(counts.following).toBe(1); // testUser2 follows testUser3
    });

    it('should return zero counts for user with no follows', async () => {
      // Create a new user with no follows
      const newUser = await UserModel.create({
        username: 'nofollow',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        first_name: 'No',
        last_name: 'Follow'
      });

      const counts = await UserFollowModel.getFollowCounts(newUser.user_id);
      expect(counts.followers).toBe(0);
      expect(counts.following).toBe(0);

      // Clean up
      await UserModel.delete(newUser.user_id);
    });
  });

  describe('getMutualFollows', () => {
    beforeEach(async () => {
      // Create mutual follows: both testUser1 and testUser2 follow testUser3
      await UserFollowModel.followUser(testUser1.user_id, testUser3.user_id);
      await UserFollowModel.followUser(testUser2.user_id, testUser3.user_id);
    });

    it('should return mutual follows', async () => {
      const mutualFollows = await UserFollowModel.getMutualFollows(testUser1.user_id, testUser2.user_id);

      expect(mutualFollows).toHaveLength(1);
      expect(mutualFollows[0].user_id).toBe(testUser3.user_id);
    });

    it('should return empty array for no mutual follows', async () => {
      // Create a new user that only testUser1 follows
      const newUser = await UserModel.create({
        username: 'nomutual',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        first_name: 'No',
        last_name: 'Mutual'
      });

      await UserFollowModel.followUser(testUser1.user_id, newUser.user_id);

      const mutualFollows = await UserFollowModel.getMutualFollows(testUser1.user_id, testUser2.user_id);
      
      // Should not include newUser since testUser2 doesn't follow them
      const mutualIds = mutualFollows.map(f => f.user_id);
      expect(mutualIds).not.toContain(newUser.user_id);

      // Clean up
      await db('user_follows').where('follower_id', testUser1.user_id).where('following_id', newUser.user_id).del();
      await UserModel.delete(newUser.user_id);
    });
  });

  describe('getRecentFollowers', () => {
    beforeEach(async () => {
      // Create follow relationships with slight delays to ensure different timestamps
      await UserFollowModel.followUser(testUser1.user_id, testUser3.user_id);
      await new Promise(resolve => setTimeout(resolve, 10));
      await UserFollowModel.followUser(testUser2.user_id, testUser3.user_id);
    });

    it('should return recent followers in chronological order', async () => {
      const recentFollowers = await UserFollowModel.getRecentFollowers(testUser3.user_id, 5);

      expect(recentFollowers).toHaveLength(2);
      
      // Should be ordered by followed_at desc (most recent first)
      expect(recentFollowers[0].user_id).toBe(testUser2.user_id);
      expect(recentFollowers[1].user_id).toBe(testUser1.user_id);
    });

    it('should respect limit parameter', async () => {
      const recentFollowers = await UserFollowModel.getRecentFollowers(testUser3.user_id, 1);
      expect(recentFollowers).toHaveLength(1);
    });
  });
});
