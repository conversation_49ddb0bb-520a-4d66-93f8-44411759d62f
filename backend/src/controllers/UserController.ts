import { Request, Response, NextFunction } from 'express';
import {
  UserModel,
  UserProfileModel,
  UserFollowModel,
  UserStatsModel,
  UserSearchModel,
  UserDashboardModel,
  UserActivityModel,
  UserValidationError,
  UserProfileValidationError
} from '@/models';
import { AuthenticatedRequest } from '@/middleware/auth';
import { logger } from '@/utils/logger';

export class UserController {
  /**
   * Get current user profile
   */
  static async getCurrentUser(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Remove sensitive data
      const { password_hash, verification_token, reset_password_token, ...safeUser } = user;

      res.json({
        success: true,
        data: safeUser
      });
    } catch (error) {
      logger.error('Error getting current user:', error);
      next(error);
    }
  }

  /**
   * Update current user
   */
  static async updateCurrentUser(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const updatedUser = await UserModel.update(userId, req.body);
      
      // Log activity
      await UserActivityModel.logActivity(userId, 'user_updated', {
        resource_type: 'user',
        resource_id: userId,
        new_values: req.body,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      // Remove sensitive data
      const { password_hash, verification_token, reset_password_token, ...safeUser } = updatedUser;

      res.json({
        success: true,
        data: safeUser,
        message: 'User updated successfully'
      });
    } catch (error) {
      if (error instanceof UserValidationError) {
        return res.status(400).json({ 
          error: error.message, 
          field: error.field 
        });
      }
      logger.error('Error updating user:', error);
      next(error);
    }
  }

  /**
   * Get user by ID (public profile)
   */
  static async getUserById(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const user = await UserModel.findById(userId);
      if (!user || user.is_deleted) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Get user profile
      const profile = await UserProfileModel.findByUserId(userId);
      
      // Check if profile is public or if requesting user is the owner
      const requestingUserId = parseInt(req.user?.id || '0', 10);
      if (profile && !profile.is_public && requestingUserId !== userId) {
        return res.status(403).json({ error: 'Profile is private' });
      }

      // Get basic stats
      const stats = await UserStatsModel.getUserStats(userId);

      // Log profile view
      if (requestingUserId && requestingUserId !== userId) {
        await UserActivityModel.logActivity(requestingUserId, 'profile_view', {
          resource_type: 'user',
          resource_id: userId,
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        });
      }

      // Return public user data
      const publicUser = {
        user_id: user.user_id,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        status: user.status,
        email_verified: user.email_verified,
        created_at: user.created_at,
        profile,
        stats: {
          followers_count: stats.followers_count,
          following_count: stats.following_count,
          events_created: stats.events_created,
          events_attended: stats.events_attended
        }
      };

      res.json({
        success: true,
        data: publicUser
      });
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      next(error);
    }
  }

  /**
   * Search users
   */
  static async searchUsers(req: Request, res: Response, next: NextFunction) {
    try {
      const filters = {
        query: req.query.q as string,
        location: req.query.location as string,
        verified_only: req.query.verified_only === 'true',
        has_events: req.query.has_events === 'true',
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        sort_by: req.query.sort_by as 'relevance' | 'followers' | 'activity' | 'created_at' || 'relevance',
        sort_order: req.query.sort_order as 'asc' | 'desc' || 'desc'
      };

      const result = await UserSearchModel.searchUsers(filters);

      res.json({
        success: true,
        data: result.data,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error) {
      logger.error('Error searching users:', error);
      next(error);
    }
  }

  /**
   * Get user discovery recommendations
   */
  static async getUserDiscovery(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const discovery = await UserSearchModel.getUserDiscovery(userId);

      res.json({
        success: true,
        data: discovery
      });
    } catch (error) {
      logger.error('Error getting user discovery:', error);
      next(error);
    }
  }

  /**
   * Get user dashboard data
   */
  static async getDashboard(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const dashboardData = await UserDashboardModel.getDashboardData(userId);

      // Log dashboard access
      await UserActivityModel.logActivity(userId, 'dashboard_accessed', {
        resource_type: 'dashboard',
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        data: dashboardData
      });
    } catch (error) {
      logger.error('Error getting dashboard data:', error);
      next(error);
    }
  }

  /**
   * Get user activity history
   */
  static async getActivityHistory(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const options = {
        limit: parseInt(req.query.limit as string) || 50,
        offset: parseInt(req.query.offset as string) || 0,
        action: req.query.action as string,
        resource_type: req.query.resource_type as string,
        dateFrom: req.query.date_from ? new Date(req.query.date_from as string) : undefined,
        dateTo: req.query.date_to ? new Date(req.query.date_to as string) : undefined
      };

      const activities = await UserActivityModel.getActivityHistory(userId, options);

      res.json({
        success: true,
        data: activities
      });
    } catch (error) {
      logger.error('Error getting activity history:', error);
      next(error);
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.params.id || req.user?.id || '0', 10);
      if (!userId) {
        return res.status(400).json({ error: 'User ID required' });
      }

      // Check if requesting user can view these stats
      const requestingUserId = parseInt(req.user?.id || '0', 10);
      if (userId !== requestingUserId) {
        // Check if profile is public
        const profile = await UserProfileModel.findByUserId(userId);
        if (!profile?.is_public) {
          return res.status(403).json({ error: 'Cannot view private user statistics' });
        }
      }

      const stats = await UserStatsModel.getUserStats(userId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting user stats:', error);
      next(error);
    }
  }

  /**
   * Get user analytics (detailed stats for own account)
   */
  static async getUserAnalytics(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const dateFrom = req.query.date_from ? new Date(req.query.date_from as string) : undefined;
      const dateTo = req.query.date_to ? new Date(req.query.date_to as string) : undefined;

      const analytics = await UserStatsModel.getUserAnalytics(userId, dateFrom, dateTo);

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error getting user analytics:', error);
      next(error);
    }
  }

  // ===== PROFILE MANAGEMENT =====

  /**
   * Get user profile
   */
  static async getProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const profile = await UserProfileModel.findByUserId(userId);
      if (!profile) {
        return res.status(404).json({ error: 'Profile not found' });
      }

      // Check if profile is public or if requesting user is the owner
      const requestingUserId = parseInt(req.user?.id || '0', 10);
      if (!profile.is_public && requestingUserId !== userId) {
        return res.status(403).json({ error: 'Profile is private' });
      }

      res.json({
        success: true,
        data: profile
      });
    } catch (error) {
      logger.error('Error getting profile:', error);
      next(error);
    }
  }

  /**
   * Create or update user profile
   */
  static async updateProfile(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Check if profile exists
      const existingProfile = await UserProfileModel.findByUserId(userId);

      let profile;
      if (existingProfile) {
        profile = await UserProfileModel.update(userId, req.body);
      } else {
        profile = await UserProfileModel.create({
          user_id: userId,
          ...req.body
        });
      }

      // Log activity
      await UserActivityModel.logActivity(userId, existingProfile ? 'profile_updated' : 'profile_created', {
        resource_type: 'user_profile',
        resource_id: profile.profile_id,
        new_values: req.body,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        data: profile,
        message: `Profile ${existingProfile ? 'updated' : 'created'} successfully`
      });
    } catch (error) {
      if (error instanceof UserProfileValidationError) {
        return res.status(400).json({
          error: error.message,
          field: error.field
        });
      }
      logger.error('Error updating profile:', error);
      next(error);
    }
  }

  /**
   * Delete user profile (set to private)
   */
  static async deleteProfile(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const success = await UserProfileModel.delete(userId);
      if (!success) {
        return res.status(404).json({ error: 'Profile not found' });
      }

      // Log activity
      await UserActivityModel.logActivity(userId, 'profile_deleted', {
        resource_type: 'user_profile',
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'Profile deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting profile:', error);
      next(error);
    }
  }

  // ===== FOLLOWING/FOLLOWER FUNCTIONALITY =====

  /**
   * Follow a user
   */
  static async followUser(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      const targetUserId = parseInt(req.params.id, 10);

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      if (isNaN(targetUserId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const follow = await UserFollowModel.followUser(userId, targetUserId);

      // Log activity
      await UserActivityModel.logActivity(userId, 'user_followed', {
        resource_type: 'user',
        resource_id: targetUserId,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        data: follow,
        message: 'User followed successfully'
      });
    } catch (error) {
      if (error instanceof Error) {
        return res.status(400).json({ error: error.message });
      }
      logger.error('Error following user:', error);
      next(error);
    }
  }

  /**
   * Unfollow a user
   */
  static async unfollowUser(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      const targetUserId = parseInt(req.params.id, 10);

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      if (isNaN(targetUserId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const success = await UserFollowModel.unfollowUser(userId, targetUserId);
      if (!success) {
        return res.status(404).json({ error: 'Follow relationship not found' });
      }

      // Log activity
      await UserActivityModel.logActivity(userId, 'user_unfollowed', {
        resource_type: 'user',
        resource_id: targetUserId,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'User unfollowed successfully'
      });
    } catch (error) {
      logger.error('Error unfollowing user:', error);
      next(error);
    }
  }

  /**
   * Get user's followers
   */
  static async getFollowers(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const options = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20
      };

      const result = await UserFollowModel.getFollowers(userId, options);

      res.json({
        success: true,
        data: result.data,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error) {
      logger.error('Error getting followers:', error);
      next(error);
    }
  }

  /**
   * Get users that a user is following
   */
  static async getFollowing(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.params.id, 10);
      if (isNaN(userId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const options = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20
      };

      const result = await UserFollowModel.getFollowing(userId, options);

      res.json({
        success: true,
        data: result.data,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error) {
      logger.error('Error getting following:', error);
      next(error);
    }
  }

  /**
   * Check if current user is following another user
   */
  static async checkFollowStatus(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      const targetUserId = parseInt(req.params.id, 10);

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      if (isNaN(targetUserId)) {
        return res.status(400).json({ error: 'Invalid user ID' });
      }

      const isFollowing = await UserFollowModel.isFollowing(userId, targetUserId);

      res.json({
        success: true,
        data: { is_following: isFollowing }
      });
    } catch (error) {
      logger.error('Error checking follow status:', error);
      next(error);
    }
  }

  // ===== USER SETTINGS =====

  /**
   * Update user settings (preferences, notifications, privacy)
   */
  static async updateSettings(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { preferences, notification_settings, privacy_settings } = req.body;

      // Get existing profile or create one
      let profile = await UserProfileModel.findByUserId(userId);
      if (!profile) {
        profile = await UserProfileModel.create({
          user_id: userId,
          language: 'en',
          is_public: true
        });
      }

      // Update settings
      const updateData: any = {};
      if (preferences) updateData.preferences = preferences;
      if (notification_settings) updateData.notification_settings = notification_settings;
      if (privacy_settings) updateData.privacy_settings = privacy_settings;

      const updatedProfile = await UserProfileModel.update(userId, updateData);

      // Log activity
      await UserActivityModel.logActivity(userId, 'settings_updated', {
        resource_type: 'user_settings',
        new_values: updateData,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        data: {
          preferences: updatedProfile.preferences,
          notification_settings: updatedProfile.notification_settings,
          privacy_settings: updatedProfile.privacy_settings
        },
        message: 'Settings updated successfully'
      });
    } catch (error) {
      logger.error('Error updating settings:', error);
      next(error);
    }
  }

  /**
   * Get user settings
   */
  static async getSettings(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const profile = await UserProfileModel.findByUserId(userId);

      res.json({
        success: true,
        data: {
          preferences: profile?.preferences || {},
          notification_settings: profile?.notification_settings || {},
          privacy_settings: profile?.privacy_settings || {}
        }
      });
    } catch (error) {
      logger.error('Error getting settings:', error);
      next(error);
    }
  }

  // ===== ACCOUNT MANAGEMENT =====

  /**
   * Deactivate user account
   */
  static async deactivateAccount(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Update user status to inactive
      await UserModel.update(userId, { status: 'inactive' });

      // Set profile to private
      const profile = await UserProfileModel.findByUserId(userId);
      if (profile) {
        await UserProfileModel.update(userId, { is_public: false });
      }

      // Log activity
      await UserActivityModel.logActivity(userId, 'account_deactivated', {
        resource_type: 'user',
        resource_id: userId,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'Account deactivated successfully'
      });
    } catch (error) {
      logger.error('Error deactivating account:', error);
      next(error);
    }
  }

  /**
   * Reactivate user account
   */
  static async reactivateAccount(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Update user status to active
      await UserModel.update(userId, { status: 'active' });

      // Log activity
      await UserActivityModel.logActivity(userId, 'account_reactivated', {
        resource_type: 'user',
        resource_id: userId,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'Account reactivated successfully'
      });
    } catch (error) {
      logger.error('Error reactivating account:', error);
      next(error);
    }
  }

  /**
   * Delete user account (soft delete)
   */
  static async deleteAccount(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { password } = req.body;
      if (!password) {
        return res.status(400).json({ error: 'Password confirmation required' });
      }

      // Verify password
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      const passwordValid = await UserModel.verifyPassword(password, user.password_hash);
      if (!passwordValid) {
        return res.status(400).json({ error: 'Invalid password' });
      }

      // Soft delete user
      await UserModel.delete(userId);

      // Deactivate all follows
      await UserFollowModel.unfollowUser(userId, userId); // This will deactivate all follows

      // Log activity
      await UserActivityModel.logActivity(userId, 'account_deleted', {
        resource_type: 'user',
        resource_id: userId,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'Account deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting account:', error);
      next(error);
    }
  }

  /**
   * Change user password
   */
  static async changePassword(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const userId = parseInt(req.user?.id || '0', 10);
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { current_password, new_password } = req.body;

      if (!current_password || !new_password) {
        return res.status(400).json({ error: 'Current password and new password are required' });
      }

      // Verify current password
      const user = await UserModel.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      const passwordValid = await UserModel.verifyPassword(current_password, user.password_hash);
      if (!passwordValid) {
        return res.status(400).json({ error: 'Current password is incorrect' });
      }

      // Update password
      await UserModel.updatePassword(userId, new_password);

      // Log activity
      await UserActivityModel.logActivity(userId, 'password_changed', {
        resource_type: 'user',
        resource_id: userId,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });

      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error) {
      if (error instanceof UserValidationError) {
        return res.status(400).json({
          error: error.message,
          field: error.field
        });
      }
      logger.error('Error changing password:', error);
      next(error);
    }
  }

  // ===== ADMIN FUNCTIONS =====

  /**
   * Get all users (admin only)
   */
  static async getAllUsers(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      // This would require admin role checking middleware
      const filters = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 50,
        status: req.query.status as string,
        email: req.query.email as string,
        username: req.query.username as string
      };

      const result = await UserModel.search(filters);

      res.json({
        success: true,
        data: result.data.map(user => {
          const { password_hash, verification_token, reset_password_token, ...safeUser } = user;
          return safeUser;
        }),
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: Math.ceil(result.total / result.limit)
        }
      });
    } catch (error) {
      logger.error('Error getting all users:', error);
      next(error);
    }
  }
}
