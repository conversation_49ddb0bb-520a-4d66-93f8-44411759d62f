version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: passa-postgres
    environment:
      POSTGRES_DB: passa_dev
      POSTGRES_USER: passa
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - passa-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: passa-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - passa-network

  # ClickHouse Analytics Database
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: passa-clickhouse
    environment:
      CLICKHOUSE_DB: passa_analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
      - ./scripts/database/clickhouse-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - passa-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: passa-backend
    environment:
      NODE_ENV: development
      DATABASE_URL: *****************************************/passa_dev
      REDIS_URL: redis://redis:6379
      CLICKHOUSE_URL: http://clickhouse:8123
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - clickhouse
    networks:
      - passa-network
    command: npm run dev

  # Frontend Web App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: passa-frontend
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - passa-network
    command: npm run dev

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: passa-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - passa-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: passa-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    ports:
      - "3002:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - passa-network

volumes:
  postgres_data:
  redis_data:
  clickhouse_data:
  prometheus_data:
  grafana_data:

networks:
  passa-network:
    driver: bridge
